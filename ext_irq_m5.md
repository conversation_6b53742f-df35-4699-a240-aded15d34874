# VK-RA6M5 External Interrupt Pins Reference

## 🎯 **External Interrupt Capabilities на VK-RA6M5**

### **Дата**: 2025-07-06
### **MCU**: Renesas RA6M5 (R7FA6M5BH3CFC)
### **Board**: VK-RA6M5 Development Board

---

## 📋 **IRQ Pin Mapping (от ra6m5_af.csv)**

### **IRQ0-IRQ15 Pin Assignments**

| IRQ# | Pin Name | Port.Bit | Arduino Pin | Board Label | Analog | Status | Notes |
|------|----------|----------|-------------|-------------|--------|--------|-------|
| **IRQ6** | P000 | P0.0 | A0 | A0 | AN000 | ✅ Available | ADC capable |
| **IRQ7** | P001 | P0.1 | A1 | A1 | AN001 | ✅ Available | ADC capable |
| **IRQ8** | P002 | P0.2 | A2 | A2 | AN002 | ✅ Available | ADC capable |
| **IRQ9** | P004 | P0.4 | - | - | AN004 | ⚠️ Check usage | ADC capable |
| **IRQ10** | P005 | P0.5 | - | - | AN005 | ⚠️ Check usage | ADC capable |
| **IRQ11** | P006 | P0.6 | - | LED1 | AN006 | ❌ Used (LED_B) | Blue LED |
| **IRQ12** | P008 | P0.8 | - | LED3 | AN008 | ❌ Used (LED_R) | Red LED |
| **IRQ13** | P009 | P0.9 | - | SW2 | AN009 | ⚠️ Used (BTN_2) | Button 2 |
| **IRQ14** | P010 | P0.10 | - | SW1 | - | ⚠️ Used (BTN_1) | Button 1 |
| **IRQ15** | P011 | P0.11 | - | - | - | ✅ Available | General purpose |

### **Additional IRQ Pins (IRQ16+)**

| IRQ# | Pin Name | Port.Bit | Arduino Pin | Board Label | Analog | Status | Notes |
|------|----------|----------|-------------|-------------|--------|--------|-------|
| **IRQ0** | P100 | P1.0 | - | - | - | ✅ Available | General purpose |
| **IRQ1** | P101 | P1.1 | - | - | - | ✅ Available | General purpose |
| **IRQ2** | P102 | P1.2 | - | - | - | ✅ Available | General purpose |
| **IRQ3** | P103 | P1.3 | - | - | - | ✅ Available | General purpose |
| **IRQ4** | P104 | P1.4 | - | - | - | ✅ Available | General purpose |
| **IRQ5** | P105 | P1.5 | - | - | - | ✅ Available | General purpose |

---

## 🔧 **Currently Configured IRQ Channels (от common_data.c)**

### **Active External IRQ Configurations:**

1. **IRQ5** - Channel 5
   - **Trigger**: Rising edge
   - **Filter**: Disabled
   - **Clock Div**: PCLK/64
   - **Priority**: 12
   - **Callback**: callback_icu

2. **IRQ7** - Channel 7
   - **Trigger**: Rising edge
   - **Filter**: Disabled
   - **Clock Div**: PCLK/64
   - **Priority**: 12
   - **Callback**: callback_icu

3. **IRQ10** - Channel 10
   - **Trigger**: Rising edge
   - **Filter**: Disabled
   - **Clock Div**: PCLK/64
   - **Priority**: 10
   - **Callback**: callback_icu

4. **IRQ12** - Channel 12
   - **Trigger**: Rising edge
   - **Filter**: Disabled
   - **Clock Div**: PCLK/64
   - **Priority**: 12
   - **Callback**: callback_icu

5. **IRQ13** - Channel 13
   - **Trigger**: Rising edge
   - **Filter**: Disabled
   - **Clock Div**: PCLK/64
   - **Priority**: 12
   - **Callback**: callback_icu

6. **IRQ14** - Channel 14
   - **Trigger**: Rising edge
   - **Filter**: Disabled
   - **Clock Div**: PCLK/64
   - **Priority**: 12
   - **Callback**: callback_icu

---

## 🎯 **Recommended Pins for External Interrupts**

### **✅ Best Options (Free & Available):**

1. **P000 (A0) - IRQ6**
   - Arduino pin A0
   - ADC capable (AN000)
   - Easy access on board
   - **Usage**: `pin = Pin('P000', Pin.IN); pin.irq(handler, Pin.IRQ_RISING)`

2. **P001 (A1) - IRQ7**
   - Arduino pin A1
   - ADC capable (AN001)
   - Easy access on board
   - **Usage**: `pin = Pin('P001', Pin.IN); pin.irq(handler, Pin.IRQ_FALLING)`

3. **P002 (A2) - IRQ8**
   - Arduino pin A2
   - ADC capable (AN002)
   - Easy access on board
   - **Usage**: `pin = Pin('P002', Pin.IN); pin.irq(handler, Pin.IRQ_RISING | Pin.IRQ_FALLING)`

4. **P011 - IRQ15**
   - General purpose pin
   - No conflicts
   - **Usage**: `pin = Pin('P011', Pin.IN); pin.irq(handler, Pin.IRQ_RISING)`

### **⚠️ Conditional Options (Check Usage):**

1. **P004 - IRQ9**
   - May be used for other functions
   - ADC capable (AN004)

2. **P005 - IRQ10**
   - May be used for other functions
   - ADC capable (AN005)

3. **P009 (SW2) - IRQ13**
   - Currently used for Button 2
   - Can be repurposed if button not needed

4. **P010 (SW1) - IRQ14**
   - Currently used for Button 1
   - Can be repurposed if button not needed

### **❌ Avoid These Pins:**

1. **P006 (LED1) - IRQ11** - Used for Blue LED
2. **P008 (LED3) - IRQ12** - Used for Red LED

---

## 💻 **MicroPython Usage Examples**

### **Basic External Interrupt Setup:**

```python
from machine import Pin

def irq_handler(pin):
    print(f"Interrupt on {pin}")

# Setup IRQ6 on P000 (A0)
pin_a0 = Pin('P000', Pin.IN, Pin.PULL_UP)
pin_a0.irq(trigger=Pin.IRQ_FALLING, handler=irq_handler)

# Setup IRQ7 on P001 (A1)
pin_a1 = Pin('P001', Pin.IN, Pin.PULL_UP)
pin_a1.irq(trigger=Pin.IRQ_RISING, handler=irq_handler)

# Setup IRQ8 on P002 (A2) - both edges
pin_a2 = Pin('P002', Pin.IN, Pin.PULL_UP)
pin_a2.irq(trigger=Pin.IRQ_RISING | Pin.IRQ_FALLING, handler=irq_handler)
```

### **Advanced Example with Counter:**

```python
from machine import Pin
import time

counter = 0

def count_pulses(pin):
    global counter
    counter += 1
    print(f"Pulse count: {counter}")

# Setup pulse counter on A0
pulse_pin = Pin('P000', Pin.IN, Pin.PULL_UP)
pulse_pin.irq(trigger=Pin.IRQ_RISING, handler=count_pulses)

print("Pulse counter ready on A0 (P000)")
while True:
    time.sleep(1)
    print(f"Total pulses: {counter}")
```

### **Multiple Interrupt Example:**

```python
from machine import Pin

def button_handler(pin):
    pin_name = str(pin).split('(')[1].split(',')[0]
    print(f"Button pressed on {pin_name}")

def sensor_handler(pin):
    pin_name = str(pin).split('(')[1].split(',')[0]
    print(f"Sensor triggered on {pin_name}")

# Setup multiple interrupts
btn1 = Pin('P000', Pin.IN, Pin.PULL_UP)  # A0 as button
btn2 = Pin('P001', Pin.IN, Pin.PULL_UP)  # A1 as button
sensor = Pin('P002', Pin.IN, Pin.PULL_UP)  # A2 as sensor

btn1.irq(trigger=Pin.IRQ_FALLING, handler=button_handler)
btn2.irq(trigger=Pin.IRQ_FALLING, handler=button_handler)
sensor.irq(trigger=Pin.IRQ_RISING, handler=sensor_handler)

print("Multiple interrupts configured:")
print("- A0 (P000): Button 1")
print("- A1 (P001): Button 2") 
print("- A2 (P002): Sensor")
```

---

## ⚙️ **Technical Specifications**

### **IRQ Capabilities:**
- **Total IRQ Lines**: 16 (IRQ0-IRQ15)
- **Trigger Modes**: Rising, Falling, Both edges, Low level
- **Priority Levels**: 0-15 (0 = highest)
- **Filter**: Optional digital filter (PCLK/64)
- **Max Frequency**: Depends on PCLK and filter settings

### **Default Configuration:**
- **Trigger**: Rising edge
- **Filter**: Disabled
- **Clock Divider**: PCLK/64
- **Priority**: 10-12
- **Callback**: callback_icu

### **Pin Characteristics:**
- **Input Voltage**: 0-3.3V
- **Pull-up/Pull-down**: Software configurable
- **Drive Strength**: Multiple levels available
- **ADC Compatible**: Many IRQ pins support ADC

---

## 🎯 **Best Practices**

### **✅ Do:**
1. Use pull-up resistors for button inputs
2. Debounce mechanical switches in software
3. Keep interrupt handlers short and fast
4. Use Pin.IRQ_FALLING for button presses
5. Test interrupt frequency limits

### **❌ Don't:**
1. Use long delays in interrupt handlers
2. Print extensively in interrupt handlers
3. Use IRQ pins already assigned to LEDs
4. Forget to configure pin direction (Pin.IN)
5. Use high-frequency interrupts without testing

### **🔧 Debugging Tips:**
1. Use LED indicators to verify interrupt firing
2. Count interrupts to check for bouncing
3. Monitor interrupt frequency with timers
4. Use oscilloscope for signal analysis
5. Check pin configuration with multimeter

---

## 📋 **Summary**

**VK-RA6M5 има отлични възможности за външни прекъсвания:**

- **16 IRQ lines** (IRQ0-IRQ15)
- **Най-добри пинове**: P000 (A0), P001 (A1), P002 (A2), P011
- **Лесна употреба** с MicroPython Pin.irq()
- **ADC съвместимост** на повечето IRQ пинове
- **Гъвкави trigger modes** и приоритети

**Препоръчани за проекти**: A0, A1, A2 пиновете са най-удобни за външни прекъсвания! 🎯
