Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
GEN build-VK_RA6M5/genhdr/mpversion.h
GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
CC ../../py/mpstate.c
CC ../../py/nlr.c
CC ../../py/nlrx86.c
CC ../../py/nlrx64.c
CC ../../py/nlrthumb.c
CC ../../py/nlraarch64.c
CC ../../py/nlrmips.c
CC ../../py/nlrpowerpc.c
CC ../../py/nlrxtensa.c
CC ../../py/nlrrv32.c
CC ../../py/nlrrv64.c
CC ../../py/nlrsetjmp.c
CC ../../py/malloc.c
CC ../../py/gc.c
CC ../../py/gc_ospi.c
CC ../../py/modgc_ospi.c
CC ../../py/pystack.c
CC ../../py/qstr.c
CC ../../py/vstr.c
CC ../../py/mpprint.c
CC ../../py/unicode.c
CC ../../py/mpz.c
CC ../../py/reader.c
CC ../../py/lexer.c
CC ../../py/parse.c
CC ../../py/scope.c
CC ../../py/compile.c
CC ../../py/emitcommon.c
CC ../../py/emitbc.c
CC ../../py/asmbase.c
CC ../../py/asmx64.c
CC ../../py/emitnx64.c
CC ../../py/asmx86.c
CC ../../py/emitnx86.c
CC ../../py/asmthumb.c
CC ../../py/emitnthumb.c
CC ../../py/emitinlinethumb.c
CC ../../py/asmarm.c
CC ../../py/emitnarm.c
CC ../../py/asmxtensa.c
CC ../../py/emitnxtensa.c
CC ../../py/emitinlinextensa.c
CC ../../py/emitnxtensawin.c
CC ../../py/asmrv32.c
CC ../../py/emitnrv32.c
CC ../../py/emitndebug.c
CC ../../py/formatfloat.c
CC ../../py/parsenumbase.c
CC ../../py/parsenum.c
CC ../../py/emitglue.c
CC ../../py/persistentcode.c
CC ../../py/runtime.c
CC ../../py/runtime_utils.c
CC ../../py/scheduler.c
CC ../../py/nativeglue.c
CC ../../py/pairheap.c
CC ../../py/ringbuf.c
CC ../../py/cstack.c
CC ../../py/stackctrl.c
CC ../../py/argcheck.c
CC ../../py/warning.c
CC ../../py/profile.c
CC ../../py/map.c
CC ../../py/obj.c
CC ../../py/objarray.c
CC ../../py/objattrtuple.c
CC ../../py/objbool.c
CC ../../py/objboundmeth.c
CC ../../py/objcell.c
CC ../../py/objclosure.c
CC ../../py/objcomplex.c
CC ../../py/objdeque.c
CC ../../py/objdict.c
CC ../../py/objenumerate.c
CC ../../py/objexcept.c
CC ../../py/objfilter.c
CC ../../py/objfloat.c
CC ../../py/objfun.c
CC ../../py/objgenerator.c
CC ../../py/objgetitemiter.c
CC ../../py/objint.c
CC ../../py/objint_longlong.c
CC ../../py/objint_mpz.c
CC ../../py/objlist.c
CC ../../py/objmap.c
CC ../../py/objmodule.c
CC ../../py/objobject.c
CC ../../py/objpolyiter.c
CC ../../py/objproperty.c
CC ../../py/objnone.c
CC ../../py/objnamedtuple.c
CC ../../py/objrange.c
CC ../../py/objreversed.c
CC ../../py/objringio.c
CC ../../py/objset.c
CC ../../py/objsingleton.c
CC ../../py/objslice.c
CC ../../py/objstr.c
CC ../../py/objstrunicode.c
CC ../../py/objstringio.c
CC ../../py/objtuple.c
CC ../../py/objtype.c
CC ../../py/objzip.c
CC ../../py/opmethods.c
CC ../../py/sequence.c
CC ../../py/stream.c
CC ../../py/binary.c
CC ../../py/builtinimport.c
CC ../../py/builtinevex.c
CC ../../py/builtinhelp.c
CC ../../py/modarray.c
CC ../../py/modbuiltins.c
CC ../../py/modcollections.c
CC ../../py/modgc.c
CC ../../py/modio.c
CC ../../py/modmath.c
CC ../../py/modcmath.c
CC ../../py/modmicropython.c
CC ../../py/modstruct.c
CC ../../py/modsys.c
CC ../../py/moderrno.c
CC ../../py/modthread.c
CC ../../py/vm.c
CC ../../py/bc.c
CC ../../py/showbc.c
CC ../../py/repl.c
CC ../../py/smallint.c
CC ../../py/frozenmod.c
CC build-VK_RA6M5/lvgl/lv_mpy.c
CC ../../extmod/machine_adc.c
CC ../../extmod/machine_adc_block.c
CC ../../extmod/machine_bitstream.c
CC ../../extmod/machine_i2c.c
CC ../../extmod/machine_i2s.c
CC ../../extmod/machine_mem.c
CC ../../extmod/machine_pinbase.c
CC ../../extmod/machine_pulse.c
CC ../../extmod/machine_pwm.c
CC ../../extmod/machine_signal.c
CC ../../extmod/machine_spi.c
CC ../../extmod/machine_timer.c
CC ../../extmod/machine_uart.c
CC ../../extmod/machine_usb_device.c
CC ../../extmod/machine_wdt.c
CC ../../extmod/modasyncio.c
CC ../../extmod/modbinascii.c
CC ../../extmod/modbluetooth.c
CC ../../extmod/modbtree.c
CC ../../extmod/modcryptolib.c
CC ../../extmod/moddeflate.c
CC ../../extmod/modframebuf.c
CC ../../extmod/modhashlib.c
CC ../../extmod/modheapq.c
CC ../../extmod/modjson.c
CC ../../extmod/modlwip.c
CC ../../extmod/modmachine.c
CC ../../extmod/modnetwork.c
CC ../../extmod/modonewire.c
CC ../../extmod/modos.c
CC ../../extmod/modplatform.c
CC ../../extmod/modrandom.c
CC ../../extmod/modre.c
CC ../../extmod/modselect.c
CC ../../extmod/modsocket.c
CC ../../extmod/modtls_axtls.c
CC ../../extmod/modtls_mbedtls.c
CC ../../extmod/modtime.c
CC ../../extmod/moductypes.c
CC ../../extmod/modvfs.c
CC ../../extmod/modwebrepl.c
CC ../../extmod/modwebsocket.c
CC ../../extmod/network_cyw43.c
CC ../../extmod/network_esp_hosted.c
CC ../../extmod/network_lwip.c
CC ../../extmod/network_ninaw10.c
CC ../../extmod/network_ppp_lwip.c
CC ../../extmod/network_wiznet5k.c
CC ../../extmod/os_dupterm.c
CC ../../extmod/vfs.c
CC ../../extmod/vfs_blockdev.c
CC ../../extmod/vfs_fat.c
CC ../../extmod/vfs_fat_diskio.c
CC ../../extmod/vfs_fat_file.c
CC ../../extmod/vfs_lfs.c
CC ../../extmod/vfs_posix.c
CC ../../extmod/vfs_posix_file.c
CC ../../extmod/vfs_reader.c
CC ../../extmod/virtpin.c
CC ../../shared/libc/abort_.c
CC ../../shared/libc/printf.c
CC ../../lib/oofatfs/ff.c
CC ../../lib/oofatfs/ffunicode.c
CC ../../shared/netutils/netutils.c
CC mbedtls/mbedtls_port.c
CC ../../shared/netutils/dhcpserver.c
CC ../../shared/netutils/trace.c
CC ../../shared/readline/readline.c
CC ../../shared/runtime/gchelper_native.c
CC ../../shared/runtime/interrupt_char.c
CC ../../shared/runtime/mpirq.c
CC ../../shared/runtime/pyexec.c
CC ../../shared/runtime/softtimer.c
In file included from ./mpconfigport.h:37,
                 from ../../py/mpconfig.h:91,
                 from ../../py/lexer.h:31,
                 from ../../py/compile.h:29,
                 from ../../shared/runtime/pyexec.c:32:
../../shared/runtime/pyexec.c: In function 'do_reader_stdin':
boards/VK_RA6M5/mpconfigboard.h:48:44: error: unsigned conversion from 'int' to 'uint16_t' {aka 'short unsigned int'} changes value from '65536' to '0' [-Werror=overflow]
   48 | #define MICROPY_REPL_STDIN_BUFFER_MAX      (64 * 1024)     /* ↑ to 64KB for 1MB+ transfers */
      |                                            ^~~~~~~~~~~
../../shared/runtime/pyexec.c:274:49: note: in expansion of macro 'MICROPY_REPL_STDIN_BUFFER_MAX'
  274 |     mp_reader_new_stdin(&reader, &reader_stdin, MICROPY_REPL_STDIN_BUFFER_MAX);
      |                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
CC ../../shared/runtime/stdout_helpers.c
cc1.exe: all warnings being treated as errors
See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/shared/runtime/pyexec.o] Error 1
make: *** Waiting for unfinished jobs....
