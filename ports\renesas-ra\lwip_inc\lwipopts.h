#ifndef MICROPY_INCLUDED_RA_LWIP_LWIPOPTS_H
#define MICROPY_INCLUDED_RA_LWIP_LWIPOPTS_H

#include <stdint.h>

// This protection is not needed, instead protect lwIP code with flags
#define SYS_ARCH_DECL_PROTECT(lev) do { } while (0)
#define SYS_ARCH_PROTECT(lev) do { } while (0)
#define SYS_ARCH_UNPROTECT(lev) do { } while (0)

#define NO_SYS                          1
#define SYS_LIGHTWEIGHT_PROT            1
#define MEM_ALIGNMENT                   4

#define LWIP_CHKSUM_ALGORITHM           3
#define LWIP_CHECKSUM_CTRL_PER_NETIF    1

#define LWIP_ARP                        1
#define LWIP_ETHERNET                   1
#define LWIP_RAW                        1
#define LWIP_NETCONN                    1    // Enable Netconn API
#define LWIP_SOCKET                     1    // Enable Socket API
#define LWIP_STATS                      1    // Enable statistics for debugging
#define LWIP_NETIF_HOSTNAME             1

#define LWIP_IPV6                       0
#define LWIP_DHCP                       1
#define LWIP_DHCP_CHECK_LINK_UP         1
#define LWIP_DHCP_DOES_ACD_CHECK        0 // to speed DHCP up
#define LWIP_DNS                        1
#define LWIP_DNS_SUPPORT_MDNS_QUERIES   1
#define LWIP_MDNS_RESPONDER             1
#define LWIP_IGMP                       1

#define LWIP_NUM_NETIF_CLIENT_DATA      LWIP_MDNS_RESPONDER
#define MEMP_NUM_UDP_PCB                (8 + LWIP_MDNS_RESPONDER)    // Increased for Socket API
#define MEMP_NUM_TCP_PCB                (8)                          // TCP PCBs for Socket API
#define MEMP_NUM_TCP_PCB_LISTEN         (4)                          // TCP listen PCBs
#define MEMP_NUM_NETCONN                (8)                          // Netconn structures
#define MEMP_NUM_SYS_TIMEOUT            (LWIP_NUM_SYS_TIMEOUT_INTERNAL + LWIP_MDNS_RESPONDER)

#define SO_REUSE                        1
#define TCP_LISTEN_BACKLOG              1

// Socket API configuration
#define LWIP_COMPAT_SOCKETS             1    // BSD socket compatibility
#define LWIP_POSIX_SOCKETS_IO_NAMES     1    // POSIX socket names
#define LWIP_SO_RCVTIMEO                1    // Socket receive timeout
#define LWIP_SO_SNDTIMEO                1    // Socket send timeout
#define LWIP_SO_RCVBUF                  1    // Socket receive buffer size control

// Statistics configuration
#define LWIP_STATS_DISPLAY              1    // Enable stats display functions
#define LINK_STATS                      1    // Link layer stats
#define ETHARP_STATS                    1    // ARP stats
#define IP_STATS                        1    // IP stats
#define IPFRAG_STATS                    1    // IP fragmentation stats
#define ICMP_STATS                      1    // ICMP stats
#define UDP_STATS                       1    // UDP stats
#define TCP_STATS                       1    // TCP stats
#define MEM_STATS                       1    // Memory stats
#define MEMP_STATS                      1    // Memory pool stats
#define SYS_STATS                       1    // System stats

extern uint32_t rng_read(void);
#define LWIP_RAND() rng_read()

#define MEM_SIZE                        (16 * 1024)
#define TCP_MSS                         (1460)
#define TCP_OVERSIZE                    (TCP_MSS)
#define TCP_WND                         (8 * TCP_MSS)
#define TCP_SND_BUF                     (8 * TCP_MSS)
#define TCP_SND_QUEUELEN                (2 * (TCP_SND_BUF / TCP_MSS))
#define TCP_QUEUE_OOSEQ                 (1)
#define MEMP_NUM_TCP_SEG                (2 * TCP_SND_QUEUELEN)

typedef uint32_t sys_prot_t;

#endif // MICROPY_INCLUDED_RA_LWIP_LWIPOPTS_H
