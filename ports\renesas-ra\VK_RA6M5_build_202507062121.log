Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
CC ../../lib/lwip/src/netif/ppp/fsm.c
CC ../../lib/lwip/src/netif/ppp/ipcp.c
CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
CC ../../lib/lwip/src/netif/ppp/lcp.c
cp: cannot stat 'build-VK_RA6M5/lib/lwip/src/netif/ppp/ipcp.d': No such file or directory
/bin/sh: line 1: build-VK_RA6M5/lib/lwip/src/netif/ppp/ipcp.d: No such file or directory
CC ../../lib/lwip/src/netif/ppp/magic.c
CC ../../lib/lwip/src/netif/ppp/mppe.c
CC ../../lib/lwip/src/netif/ppp/multilink.c
CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
CC ../../lib/lwip/src/netif/ppp/pppapi.c
CC ../../lib/lwip/src/netif/ppp/ppp.c
CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
cp: cannot stat 'build-VK_RA6M5/lib/lwip/src/netif/ppp/pppapi.d': No such file or directory
/bin/sh: line 1: build-VK_RA6M5/lib/lwip/src/netif/ppp/pppapi.d: No such file or directory
CC ../../lib/lwip/src/netif/ppp/pppoe.c
CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
CC ../../lib/lwip/src/netif/ppp/pppos.c
CC ../../lib/lwip/src/netif/ppp/upap.c
CC ../../lib/lwip/src/netif/ppp/utils.c
CC ../../lib/lwip/src/netif/ppp/vj.c
CC ../../shared/netutils/dhcpserver.c
cp: cannot stat 'build-VK_RA6M5/lib/lwip/src/netif/ppp/upap.d': No such file or directory
/bin/sh: line 1: build-VK_RA6M5/lib/lwip/src/netif/ppp/upap.d: No such file or directory
CC main.c
CC mpnetworkport.c
CC network_lan.c
CC eth.c
  GEN     objects.rsp
LINK build-VK_RA6M5/firmware.elf
C:\Program Files (x86)\GNU Tools Arm Embedded\9 2019-q4-major\bin\arm-none-eabi-ld.exe: cannot find @build-VK_RA6M5/objects.rsp: No such file or directory
make: *** [Makefile:661: build-VK_RA6M5/firmware.elf] Error 1
