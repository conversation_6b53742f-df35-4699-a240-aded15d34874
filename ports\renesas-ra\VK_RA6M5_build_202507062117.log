Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
CC ../../extmod/modlwip.c
CC ../../extmod/modnetwork.c
CC ../../extmod/network_lwip.c
CC ../../extmod/network_ppp_lwip.c
CC ../../lib/lwip/src/apps/mdns/mdns.c
CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
CC ../../lib/lwip/src/apps/mdns/mdns_out.c
CC ../../lib/lwip/src/core/def.c
CC ../../lib/lwip/src/core/dns.c
CC ../../lib/lwip/src/core/inet_chksum.c
CC ../../lib/lwip/src/core/init.c
CC ../../lib/lwip/src/core/ip.c
../../lib/lwip/src/core/init.c:176:2: error: #error "If you want to use Sequential API, you have to define NO_SYS=0 in your lwipopts.h"
  176 | #error "If you want to use Sequential API, you have to define NO_SYS=0 in your lwipopts.h"
      |  ^~~~~
See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/lib/lwip/src/core/init.o] Error 1
make: *** Waiting for unfinished jobs....
