diff --git a/0001-gc_ospi-reset-negative-cache-after-heap-is-added.patch b/0001-gc_ospi-reset-negative-cache-after-heap-is-added.patch
new file mode 100644
index 00000000..bcde690e
--- /dev/null
+++ b/0001-gc_ospi-reset-negative-cache-after-heap-is-added.patch
@@ -0,0 +1,37 @@
+From 8a6e0c88f9f8383e71d331d6c8ca0d29d9f7b211 Mon Sep 17 00:00:00 2001
+Subject: gc_ospi: reset negative cache right after heap is added
+Date: Sun, 22 Jun 2025 12:34:00 +0300
+Message-Id: <<EMAIL>>
+X-Mailer: git am
+
+When gc_ospi_init() succeeds it must invalidate the
+`cached_ospi_area == (void*)-1` sentinel, otherwise later calls to
+find_ospi_area() will still think the OSPI region is absent.
+
+---
+ py/gc_ospi.c | 6 ++++++
+ 1 file changed, 6 insertions(+)
+
+diff --git a/py/gc_ospi.c b/py/gc_ospi.c
+index 12345678..abcd9876 100644
+--- a/py/gc_ospi.c
++++ b/py/gc_ospi.c
+@@
+     gc_add((void *)start_addr, (void *)(start_addr + bytes));
+
+     ospi_heap_added = true;     /* маркираме като добавено */
+
++    /* ❶ OSPI-heap вече съществува → изчисти „negative-cache“ sentinel.
++     *    Това позволява find_ospi_area() да намери областта при
++     *    първото последващо извикване.                               */
++    if (cached_ospi_area == (mp_state_mem_area_t*)-1) {
++        cached_ospi_area = NULL;
++    }
++
+     size_t blocks = bytes / BYTES_PER_BLOCK;
+     mp_printf(&mp_plat_print,
+               "[OSPI_GC] Init: Added %u blocks (%u MB) from 0x%08x to GC heap\n",
+               (unsigned)blocks, (unsigned)(bytes >> 20),
+               (unsigned)start_addr);
+--
+2.43.0
diff --git a/0002-main-call-gc_ospi_init-before-first-allocation.patch b/0002-main-call-gc_ospi_init-before-first-allocation.patch
new file mode 100644
index 00000000..df55025c
--- /dev/null
+++ b/0002-main-call-gc_ospi_init-before-first-allocation.patch
@@ -0,0 +1,27 @@
+From 62c41f7d6e2f46b0bea1b17fe55bfe7b6f362aa0 Mon Sep 17 00:00:00 2001
+Subject: renesas-ra/main: call gc_ospi_init() before Python starts
+Date: Sun, 22 Jun 2025 12:34:50 +0300
+Message-Id: <<EMAIL>>
+X-Mailer: git am
+
+Ensures the external OSPI heap is registered *once* on every boot, so
+that large allocations immediately land in external RAM.
+
+---
+ ports/renesas-ra/main.c | 5 +++++
+ 1 file changed, 5 insertions(+)
+
+diff --git a/ports/renesas-ra/main.c b/ports/renesas-ra/main.c
+index 9abc4321..def06789 100644
+--- a/ports/renesas-ra/main.c
++++ b/ports/renesas-ra/main.c
+@@
+     mp_hal_set_interrupt_char(CHAR_CTRL_C);
+
+     gc_init(heap_start, heap_end);
++#if MICROPY_HW_HAS_OSPI_RAM
++#include "py/gc_ospi.h"
++    gc_ospi_init();         // ⬅ добави външния heap преди първата алокация
++#endif
++
+     mp_init();
diff --git a/examples/debug_restart.py b/examples/debug_restart.py
new file mode 100644
index 00000000..edbefc8f
--- /dev/null
+++ b/examples/debug_restart.py
@@ -0,0 +1,188 @@
+# debug_restart.py – Debug script for reset-cause + memory-map stress on VK-RA6M5
+# Version: 0.5.2 (2025-06-21)
+#
+# Features:
+# - Reset cause detection and logging
+# - Memory stress testing with 8 MB OSPI window
+# - Two-pass gradient memory pattern testing
+# - OSPI GC statistics and fragmentation analysis
+# - Banner fixes and improved output formatting
+#
+# Usage:
+#   import examples.debug_restart
+#   examples.debug_restart.run_stress_test()
+
+import gc
+import machine
+import time
+import sys
+
+# Version info
+VERSION = "0.5.2"
+BUILD_DATE = "2025-06-21"
+
+def print_banner():
+    """Print formatted banner with version info"""
+    print("=" * 60)
+    print(f"VK-RA6M5 Debug & Stress Test v{VERSION}")
+    print(f"Build: {BUILD_DATE}")
+    print("=" * 60)
+
+def get_reset_cause():
+    """Detect and return reset cause"""
+    try:
+        # Try to get reset cause from machine module
+        if hasattr(machine, 'reset_cause'):
+            cause = machine.reset_cause()
+            causes = {
+                machine.PWRON_RESET: "Power-on Reset",
+                machine.HARD_RESET: "Hard Reset", 
+                machine.WDT_RESET: "Watchdog Reset",
+                machine.DEEPSLEEP_RESET: "Deep Sleep Reset",
+                machine.SOFT_RESET: "Soft Reset"
+            }
+            return causes.get(cause, f"Unknown Reset ({cause})")
+        else:
+            return "Reset cause detection not available"
+    except Exception as e:
+        return f"Error detecting reset cause: {e}"
+
+def memory_info():
+    """Get comprehensive memory information"""
+    print("\n--- Memory Information ---")
+    
+    # Standard GC info
+    gc.collect()
+    mem_free = gc.mem_free()
+    mem_alloc = gc.mem_alloc()
+    mem_total = mem_free + mem_alloc
+    
+    print(f"Main SRAM: {mem_alloc:,} used, {mem_free:,} free, {mem_total:,} total")
+    print(f"Utilization: {(mem_alloc/mem_total)*100:.1f}%")
+    
+    # OSPI GC stats if available
+    try:
+        import gc_ospi
+        print("\n--- OSPI Memory Stats ---")
+        gc_ospi.stats()
+    except ImportError:
+        print("OSPI GC module not available")
+    except Exception as e:
+        print(f"OSPI stats error: {e}")
+
+def stress_test_pattern(size_mb=4, pattern_type="gradient"):
+    """
+    Stress test memory with specific patterns
+    
+    Args:
+        size_mb: Size in MB to allocate
+        pattern_type: "gradient", "random", or "alternating"
+    """
+    print(f"\n--- Stress Test: {pattern_type.title()} Pattern ({size_mb} MB) ---")
+    
+    try:
+        # Calculate allocation size
+        alloc_size = size_mb * 1024 * 1024
+        chunk_size = 64 * 1024  # 64KB chunks
+        num_chunks = alloc_size // chunk_size
+        
+        print(f"Allocating {num_chunks} chunks of {chunk_size:,} bytes each...")
+        
+        chunks = []
+        start_time = time.ticks_ms()
+        
+        for i in range(num_chunks):
+            if pattern_type == "gradient":
+                # Two-pass gradient pattern
+                data = bytearray(chunk_size)
+                # First pass: linear gradient
+                for j in range(chunk_size):
+                    data[j] = (j + i) & 0xFF
+                # Second pass: reverse gradient overlay
+                for j in range(0, chunk_size, 2):
+                    data[j] = (255 - data[j]) & 0xFF
+                    
+            elif pattern_type == "alternating":
+                # Alternating 0x55/0xAA pattern
+                pattern = 0x55 if i % 2 == 0 else 0xAA
+                data = bytearray([pattern] * chunk_size)
+                
+            elif pattern_type == "random":
+                # Pseudo-random pattern based on chunk index
+                import random
+                random.seed(i)
+                data = bytearray([random.randint(0, 255) for _ in range(chunk_size)])
+            
+            chunks.append(data)
+            
+            # Progress indicator
+            if (i + 1) % 10 == 0:
+                elapsed = time.ticks_diff(time.ticks_ms(), start_time)
+                print(f"  Progress: {i+1}/{num_chunks} chunks ({elapsed} ms)")
+        
+        elapsed = time.ticks_diff(time.ticks_ms(), start_time)
+        print(f"Allocation completed in {elapsed} ms")
+        
+        # Memory verification
+        print("Verifying data integrity...")
+        verify_start = time.ticks_ms()
+        
+        for i, chunk in enumerate(chunks):
+            if pattern_type == "gradient":
+                # Verify gradient pattern
+                for j in range(0, len(chunk), 100):  # Sample verification
+                    expected = ((j + i) & 0xFF) if j % 2 != 0 else ((255 - ((j + i) & 0xFF)) & 0xFF)
+                    if chunk[j] != expected:
+                        print(f"Data corruption detected in chunk {i}, offset {j}")
+                        break
+                        
+        verify_elapsed = time.ticks_diff(time.ticks_ms(), verify_start)
+        print(f"Verification completed in {verify_elapsed} ms")
+        
+        # Cleanup
+        del chunks
+        gc.collect()
+        print("Memory released and collected")
+        
+    except MemoryError as e:
+        print(f"Memory allocation failed: {e}")
+        gc.collect()
+    except Exception as e:
+        print(f"Stress test error: {e}")
+        gc.collect()
+
+def run_comprehensive_test():
+    """Run comprehensive memory and system test"""
+    print_banner()
+    
+    # System info
+    print(f"Reset cause: {get_reset_cause()}")
+    print(f"Python version: {sys.version}")
+    print(f"Platform: {sys.platform}")
+    
+    # Initial memory state
+    memory_info()
+    
+    # Run stress tests
+    patterns = ["gradient", "alternating", "random"]
+    sizes = [2, 4, 6]  # MB
+    
+    for pattern in patterns:
+        for size in sizes:
+            stress_test_pattern(size, pattern)
+            memory_info()
+            time.sleep(1)  # Brief pause between tests
+    
+    print("\n" + "=" * 60)
+    print("Comprehensive test completed!")
+    print("=" * 60)
+
+def run_stress_test():
+    """Quick stress test entry point"""
+    print_banner()
+    memory_info()
+    stress_test_pattern(4, "gradient")
+    memory_info()
+
+if __name__ == "__main__":
+    run_comprehensive_test()
diff --git a/fix_gc_ospi.patch b/fix_gc_ospi.patch
deleted file mode 100644
index 12a0e47f..00000000
--- a/fix_gc_ospi.patch
+++ /dev/null
@@ -1,57 +0,0 @@
-diff --git a/py/gc.c b/py/gc.c
-@@
--/* ------------------------------------------------------------------------- */
--/*  !!!  THESE THREE DEFINITIONS DUPLICATE THE ALIASИ И ВОДЯТ ДО REDEF  !!!  */
--/* ------------------------------------------------------------------------- */
--void *gc_alloc_default  (size_t n, unsigned int f) { return gc_alloc  (n, f); }
--void  gc_free_default   (void *p)                 {        gc_free   (p);    }
--void *gc_realloc_default(void *p, size_t n, bool m){return gc_realloc(p,n,m);}
--
--/* ------------------------------------------------------------------------- */
-+/*  (alias-декларациите по-горе са напълно достатъчни –                 */
-+/*   реални тела НЕ са нужни и водят до „redefinition of …” )           */
-+
-diff --git a/py/gc_ospi.c b/py/gc_ospi.c
-@@
--/* -------- weak-override macro ------------------------------------------ */
--#ifndef MICROPY_GC_WEAK
--#define MICROPY_GC_WEAK  __attribute__((weak))
--#endif
--
--/* -------- FWD declaration (не ни трябва целият struct) ----------------- */
--typedef struct _mp_state_mem_area_t mp_state_mem_area_t;
--extern mp_state_mem_area_t *gc_get_ptr_area(const void *ptr);
--
--/* -----------  GLOBALS  --------------------------------------------------- */
--STATIC size_t            gc_free_bytes       = 0;
--STATIC mp_uint_t         last_validate_ms    = 0;
--STATIC bool              ospi_emergency_mode = false;
--STATIC ospi_gc_stats_t   ospi_gc_stats;
--STATIC gc_pressure_stats_t gc_pressure_stats;
--
--/* …  (цялото съдържание от канваса – без промени) …                       */
-+/* -------------------------------------------------------------------- */
-+/*  Weak-attribute за override-ите                                       */
-+/* -------------------------------------------------------------------- */
-+#ifndef MICROPY_GC_WEAK
-+#define MICROPY_GC_WEAK __attribute__((weak))
-+#endif
-+
-+/* --- само лек forward-decl, реалният struct e в gc.c ------------------ */
-+typedef struct _mp_state_mem_area_t mp_state_mem_area_t;
-+extern mp_state_mem_area_t *gc_get_ptr_area(const void *ptr);
-+
-+/* --- силните символи от gc.c, за да няма implicit-function warnings --- */
-+extern void *gc_alloc_default (size_t, unsigned int);
-+extern void  gc_free_default  (void *);
-+extern void *gc_realloc_default(void *, size_t, bool);
-+
-+/* -----------  GLOBALS (OSPI-only)  ------------------------------------ */
-+STATIC size_t             gc_free_bytes       = 0;
-+STATIC mp_uint_t          last_validate_ms    = 0;
-+STATIC bool               ospi_emergency_mode = false;
-+STATIC ospi_gc_stats_t    ospi_gc_stats       = {0};
-+STATIC gc_pressure_stats_t gc_pressure_stats  = {0};
-+
-+/* (цялото останало съдържание остава непроменено – helper-и,           */
-+/*  gc_alloc/free/realloc + gc_ospi_on_init и пр.)                      */
diff --git a/gc_allocation_fix.patch b/gc_allocation_fix.patch
deleted file mode 100644
index 135631c7..00000000
--- a/gc_allocation_fix.patch
+++ /dev/null
@@ -1,235 +0,0 @@
-diff --git a/py/gc.c b/py/gc.c
---- a/py/gc.c
-+++ b/py/gc.c
-@@
- #include "py/runtime.h"
-+
-+/* -----------------------------------------------------------------------
-+ * Optional OSPI extensions
-+ * -------------------------------------------------------------------- */
-+#ifdef MICROPY_PORT_RA6M5_OSPI
-+#include "py/gc_ospi.h"      /* declares gc_ospi_on_init() + weak symbols */
-+#endif
-diff --git a/py/gc_ospi.h b/py/gc_ospi.h
-new file mode 100644
---- /dev/null
-+++ b/py/gc_ospi.h
-@@
-+#ifndef MICROPY_INCLUDED_GC_OSPI_H
-+#define MICROPY_INCLUDED_GC_OSPI_H
-+
-+#include <stddef.h>
-+#include <stdbool.h>
-+
-+/*  Called once from the port-specific gc_init() wrapper to
-+ *  initialise OSPI-side statistics and byte-counters.               */
-+void gc_ospi_on_init(void);
-+
-+#endif /* MICROPY_INCLUDED_GC_OSPI_H */
-diff --git a/py/gc_ospi_internal.h b/py/gc_ospi_internal.h
-new file mode 100644
---- /dev/null
-+++ b/py/gc_ospi_internal.h
-@@
-+/*
-+ * gc_ospi_internal.h – shared helpers for the external-OSPI GC layer
-+ * (included by both gc_ospi.c and, when necessary, other C-files)
-+ */
-+#ifndef MICROPY_GC_OSPI_INTERNAL_H
-+#define MICROPY_GC_OSPI_INTERNAL_H
-+
-+#include "py/gc.h"          /* pulls in MICROPY_BYTES_PER_GC_BLOCK & friends */
-+
-+/* -------- basic GC constants we need outside gc.c --------------------- */
-+
-+#ifndef WORDS_PER_BLOCK
-+#define WORDS_PER_BLOCK   ((MICROPY_BYTES_PER_GC_BLOCK) / MP_BYTES_PER_OBJ_WORD)
-+#endif
-+#ifndef BYTES_PER_BLOCK
-+#define BYTES_PER_BLOCK   (MICROPY_BYTES_PER_GC_BLOCK)
-+#endif
-+
-+#define AT_FREE           (0)
-+#define AT_HEAD           (1)
-+#define AT_TAIL           (2)
-+#define AT_MARK           (3)
-+
-+#define BLOCKS_PER_ATB    (4)
-+
-+#define BLOCK_SHIFT(block)      (2 * ((block) & (BLOCKS_PER_ATB - 1)))
-+#define ATB_GET_KIND(area, blk) (((area)->gc_alloc_table_start[(blk) / BLOCKS_PER_ATB] >> \
-+                                  BLOCK_SHIFT(blk)) & 3)
-+
-+#define OSPI_EMERGENCY_RESERVE  (8 * 1024)   /* 8 KB safety head-room */
-+
-+/* ---- statistics structures ------------------------------------------ */
-+typedef struct {
-+    size_t alloc_count;
-+    size_t free_count;
-+    size_t total_allocated;
-+    size_t current_allocated;
-+    size_t max_free_block;
-+} ospi_gc_stats_t;
-+
-+typedef struct {
-+    size_t events[16];
-+} gc_pressure_stats_t;
-+
-+/* ---- helpers implemented in gc_ospi.c ------------------------------- */
-+bool   is_ospi_area(struct _mp_state_mem_area_t *area);
-+size_t ospi_align_blocks(size_t blocks);
-+void   ospi_update_stats(size_t bytes, bool is_alloc);
-+void   ospi_update_free_stats(size_t size);
-+void   update_pressure_metrics(size_t bytes, bool success);
-+
-+#endif /* MICROPY_GC_OSPI_INTERNAL_H */
-diff --git a/py/gc_ospi.c b/py/gc_ospi.c
-new file mode 100644
---- /dev/null
-+++ b/py/gc_ospi.c
-@@
-+/*
-+ * gc_ospi.c – OSPI-optimised GC overrides for the VK-RA6M5 port
-+ * ---------------------------------------------------------------------------
-+ * This file is compiled **in addition to** the stock py/gc.c and therefore
-+ * provides *weak* versions of the public GC API (gc_alloc/gc_free/gc_realloc)
-+ * so the linker will automatically pick them only when
-+ * MICROPY_PORT_RA6M5_OSPI is enabled.
-+ */
-+
-+#include "py/mpconfig.h"
-+#if defined(MICROPY_PORT_RA6M5_OSPI) && MICROPY_ENABLE_GC
-+
-+#include <assert.h>
-+#include <stdbool.h>
-+#include <stddef.h>
-+#include <stdint.h>
-+#include <string.h>
-+
-+#include "py/gc.h"
-+#include "py/gc_ospi_internal.h"
-+#include "py/mpstate.h"
-+#include "py/mphal.h"
-+#include "py/runtime.h"
-+
-+/* forward decls for strong symbols that live in gc.c */
-+extern void *gc_alloc_default(size_t, unsigned int);
-+extern void  gc_free_default(void *);
-+extern void *gc_realloc_default(void *, size_t, bool);
-+extern struct _mp_state_mem_area_t *gc_get_ptr_area(const void *);
-+
-+/* ---- weak-override attribute ----------------------------------------- */
-+#ifndef MICROPY_GC_WEAK
-+#define MICROPY_GC_WEAK  __attribute__((weak))
-+#endif
-+
-+/* ---- lightweight debug macros ---------------------------------------- */
-+#ifndef DEBUG_OSPI_GC_LEVEL
-+#define DEBUG_OSPI_GC_LEVEL 0
-+#endif
-+#if DEBUG_OSPI_GC_LEVEL >= 1
-+#define DBG1(...) mp_printf(&mp_plat_print, __VA_ARGS__)
-+#else
-+#define DBG1(...)
-+#endif
-+
-+/* --------------------------------------------------------------------- */
-+/*                   Tiny helpers & private state                        */
-+/* --------------------------------------------------------------------- */
-+STATIC size_t     gc_free_bytes       = 0;
-+STATIC mp_uint_t  last_validate_ms    = 0;
-+STATIC bool       ospi_emergency_mode = false;
-+STATIC ospi_gc_stats_t     ospi_gc_stats      = {0};
-+STATIC gc_pressure_stats_t gc_pressure_stats = {0};
-+
-+STATIC inline bool ospi_check_critical_memory_fast(void) {
-+    return gc_free_bytes < OSPI_EMERGENCY_RESERVE && !ospi_emergency_mode;
-+}
-+
-+STATIC inline void gc_update_free_bytes(ssize_t delta) {
-+    if (delta < 0 && (size_t)(-delta) > gc_free_bytes) {
-+        gc_free_bytes = 0;
-+    } else {
-+        gc_free_bytes += delta;
-+    }
-+}
-+
-+bool MICROPY_GC_WEAK is_ospi_area(struct _mp_state_mem_area_t *area) {
-+    /* adapt the base-address check to your memory-map if needed */
-+    return (uintptr_t)area->gc_pool_start >= 0x90000000u;
-+}
-+
-+size_t MICROPY_GC_WEAK ospi_align_blocks(size_t blocks) {
-+    return (blocks + 7) & ~((size_t)7);
-+}
-+
-+void MICROPY_GC_WEAK ospi_update_stats(size_t bytes, bool is_alloc) {
-+    if (is_alloc) {
-+        ospi_gc_stats.alloc_count++;
-+        ospi_gc_stats.total_allocated   += bytes;
-+        ospi_gc_stats.current_allocated += bytes;
-+    } else {
-+        ospi_gc_stats.free_count++;
-+        if (bytes > ospi_gc_stats.current_allocated) {
-+            ospi_gc_stats.current_allocated = 0;
-+        } else {
-+            ospi_gc_stats.current_allocated -= bytes;
-+        }
-+    }
-+}
-+
-+void MICROPY_GC_WEAK ospi_update_free_stats(size_t size) {
-+    if (size > ospi_gc_stats.max_free_block) {
-+        ospi_gc_stats.max_free_block = size;
-+    }
-+}
-+
-+void MICROPY_GC_WEAK update_pressure_metrics(size_t bytes, bool success) {
-+    (void)bytes;
-+    (void)success;
-+}
-+
-+/* --------------------------------------------------------------------- */
-+/*                    Weak public-API overrides                           */
-+/* --------------------------------------------------------------------- */
-+
-+void *MICROPY_GC_WEAK gc_alloc(size_t n_bytes, unsigned int alloc_flags) {
-+    if (ospi_check_critical_memory_fast()) {
-+        ospi_emergency_mode = true;
-+    }
-+    /* For now always fall-back to default allocator.  */
-+    return gc_alloc_default(n_bytes, alloc_flags);
-+}
-+
-+void MICROPY_GC_WEAK gc_free(void *ptr) {
-+    if (ptr != NULL) {
-+        struct _mp_state_mem_area_t *area =
-+#if MICROPY_GC_SPLIT_HEAP
-+            gc_get_ptr_area(ptr);
-+#else
-+            &MP_STATE_MEM(area);
-+#endif
-+        if (area && is_ospi_area(area)) {
-+            gc_update_free_bytes(BYTES_PER_BLOCK);
-+            ospi_update_stats(BYTES_PER_BLOCK, false);
-+        }
-+    }
-+    gc_free_default(ptr);
-+}
-+
-+void *MICROPY_GC_WEAK gc_realloc(void *ptr, size_t n_bytes, bool allow_move) {
-+    return gc_realloc_default(ptr, n_bytes, allow_move);
-+}
-+
-+/* --------------------------------------------------------------------- */
-+/*                       One-time initialiser                             */
-+/* --------------------------------------------------------------------- */
-+void gc_ospi_on_init(void) {
-+    memset(&ospi_gc_stats, 0, sizeof(ospi_gc_stats));
-+    memset(&gc_pressure_stats, 0, sizeof(gc_pressure_stats));
-+    ospi_emergency_mode = false;
-+    last_validate_ms    = 0;
-+    gc_free_bytes       = 0;   /* real counter can be filled later */
-+}
-+
-+#endif /* MICROPY_PORT_RA6M5_OSPI && MICROPY_ENABLE_GC */
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212217.log b/ports/renesas-ra/VK_RA6M5_build_202506212217.log
new file mode 100644
index 00000000..d0734fd1
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212217.log
@@ -0,0 +1,24 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC ../../py/modsys.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../shared/runtime/pyexec.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212224.log b/ports/renesas-ra/VK_RA6M5_build_202506212224.log
new file mode 100644
index 00000000..cb6359f9
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212224.log
@@ -0,0 +1,1130 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+LVGL-GEN build-VK_RA6M5/lvgl/lv_mpy.c
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/ra_gen/
+mkdir -p build-VK_RA6M5/build-VK_RA6M5/lvgl/
+mkdir -p build-VK_RA6M5/drivers/bus/
+mkdir -p build-VK_RA6M5/drivers/dht/
+mkdir -p build-VK_RA6M5/drivers/memory/
+mkdir -p build-VK_RA6M5/extmod/
+mkdir -p build-VK_RA6M5/extmod/mbedtls/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/mcu/all/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_dtc/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_flash_hp/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ioport/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_lpm/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ospi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_qspi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sci_uart/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sdhi/
+mkdir -p build-VK_RA6M5/lib/libm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/core/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/display/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/dma2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nema_gfx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/g2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/pxp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/vglite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/opengles/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/renesas/dave2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/blend/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/vg_lite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/drm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/fb/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ft81x/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ili9341/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/lcd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7735/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7789/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7796/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/evdev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/glfw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/libinput/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/nuttx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/qnx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/wayland/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/windows/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/x11/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/font/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/indev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/flex/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/grid/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/barcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bin_decoder/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bmp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/expat/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/ffmpeg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/freetype/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/fsdrv/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/gif/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libpng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lodepng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lz4/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/qrcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rle/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rlottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/svg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tiny_ttf/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tjpgd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/class/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/instance/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/osal/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/file_explorer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/font_manager/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/fragment/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/gridnav/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/ime/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/imgfont/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/monkey/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/observer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/snapshot/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/sysmon/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/test/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/vg_lite_tvg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/parsers/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/builtin/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/clib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/micropython/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/rtthread/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/default/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/mono/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/simple/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/tick/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/3dtexture/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/animimage/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/arc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/bar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/button/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/buttonmatrix/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/calendar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/canvas/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/chart/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/checkbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/dropdown/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/image/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/imagebutton/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/keyboard/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/label/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/led/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/line/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/list/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/lottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/menu/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/msgbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/objx_templ/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/property/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/roller/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/scale/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/slider/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/span/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinner/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/switch/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/table/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tabview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/textarea/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tileview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/win/
+mkdir -p build-VK_RA6M5/lib/lwip/src/apps/mdns/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv4/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv6/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/polarssl/
+mkdir -p build-VK_RA6M5/lib/mbedtls/library/
+mkdir -p build-VK_RA6M5/lib/mbedtls_errors/
+mkdir -p build-VK_RA6M5/lib/oofatfs/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/cdc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/dfu/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/hid/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/midi/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/msc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/usbtmc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/vendor/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/common/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/device/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/portable/renesas/rusb2/
+mkdir -p build-VK_RA6M5/mbedtls/
+mkdir -p build-VK_RA6M5/py/
+mkdir -p build-VK_RA6M5/ra/
+mkdir -p build-VK_RA6M5/shared/libc/
+mkdir -p build-VK_RA6M5/shared/netutils/
+mkdir -p build-VK_RA6M5/shared/readline/
+mkdir -p build-VK_RA6M5/shared/runtime/
+mkdir -p build-VK_RA6M5/shared/timeutils/
+mkdir -p build-VK_RA6M5/shared/tinyusb/
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data updated
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/compressed.data.h
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+Module registrations updated
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/moduledefs.h
+Root pointer registrations updated
+QSTR updated
+GEN build-VK_RA6M5/genhdr/root_pointers.h
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+CC ../../extmod/machine_bitstream.c
+MPY asyncio/__init__.py
+MPY asyncio/core.py
+MPY asyncio/event.py
+MPY asyncio/funcs.py
+MPY asyncio/lock.py
+MPY asyncio/stream.py
+MPY uasyncio.py
+MPY dht.py
+MPY onewire.py
+GEN build-VK_RA6M5/frozen_content.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modopenamp.c
+CC ../../extmod/modopenamp_remoteproc.c
+CC ../../extmod/modopenamp_remoteproc_store.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/mbedtls/mbedtls_alt.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_group.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_class.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_draw.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_event.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_id_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_pos.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_property.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_tree.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_refr.c
+CC ../../lib/lv_bindings/lvgl/src/display/lv_display.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_3d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_image_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_stm32_hal.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_nema_gfx_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_buf_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_buf_map.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_osa.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/opengles/lv_draw_opengles.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sdl/lv_draw_sdl.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888_premultiplied.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565_swapped.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_letter.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_transform.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_math.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_pending.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/drm/lv_linux_drm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/fb/lv_linux_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ft81x/lv_ft81x.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ili9341/lv_ili9341.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7735/lv_st7735.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7789/lv_st7789.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7796/lv_st7796.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/lv_st_ltdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/evdev/lv_evdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_glfw_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_debug.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_driver.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_texture.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_libinput.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_xkb.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_entry.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_lcd.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_libuv.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_profiler.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/qnx/lv_qnx.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mouse.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mousewheel.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_private.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland_smm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_dmabuf.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer_axis.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_seat.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window_decorations.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_xdg_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_input.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_input.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_binfont_loader.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_fmt_txt.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_10.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_12.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14_aligned.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_18.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_20.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_22.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_24.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_26.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28_compressed.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_30.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_32.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_34.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_36.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_38.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_40.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_42.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_44.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_46.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_48.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_8.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_8.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/flex/lv_flex.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/grid/lv_grid.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/lv_layout.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/code128.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/lv_barcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bin_decoder/lv_bin_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bmp/lv_bmp.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlparse.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlrole.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_impl.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_ns.c
+CC ../../lib/lv_bindings/lvgl/src/libs/ffmpeg/lv_ffmpeg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_glyph.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_image.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_outline.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_ftsystem.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_cbfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_littlefs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_memfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_posix.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_stdio.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_win32.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/gifdec.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/lv_gif.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libpng/lv_libpng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lv_lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lz4/lz4.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/lv_qrcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/qrcodegen.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rle/lv_rle.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rlottie/lv_rlottie.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_parser.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_render.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_token.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/lv_tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/lv_init.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_header_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache_entry.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim_timeline.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_area.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_array.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_async.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_bidi.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_circle_buf.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color_op.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_event.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_fs.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_grad.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_iter.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_log.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_lru.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_math.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_palette.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_profiler_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_templ.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text_ap.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_timer.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_tree.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_utils.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_cmsis_rtos2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_freertos.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_linux.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_mqx.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os_none.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_pthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_sdl2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_windows.c
+CC ../../lib/lv_bindings/lvgl/src/others/file_explorer/lv_file_explorer.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager_recycle.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/gridnav/lv_gridnav.c
+CC ../../lib/lv_bindings/lvgl/src/others/ime/lv_ime_pinyin.c
+CC ../../lib/lv_bindings/lvgl/src/others/imgfont/lv_imgfont.c
+CC ../../lib/lv_bindings/lvgl/src/others/monkey/lv_monkey.c
+CC ../../lib/lv_bindings/lvgl/src/others/observer/lv_observer.c
+CC ../../lib/lv_bindings/lvgl/src/others/snapshot/lv_snapshot.c
+CC ../../lib/lv_bindings/lvgl/src/others/sysmon/lv_sysmon.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_display.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_helpers.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_screenshot_compare.c
+CC ../../lib/lv_bindings/lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_base_types.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_component.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_style.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_update.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_utils.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_widget.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_arc_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_bar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_buttonmatrix_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_button_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_calendar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_canvas_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_chart_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_checkbox_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_dropdown_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_event_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_image_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_keyboard_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_label_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_obj_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_roller_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_scale_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_slider_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_spangroup_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_table_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_tabview_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_textarea_parser.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_mem_core_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_sprintf_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_string_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_tlsf.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_mem_core_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_sprintf_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_string_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/lv_mem.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/micropython/lv_mem_core_micropython.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_string_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/uefi/lv_mem_core_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/themes/default/lv_theme_default.c
+CC ../../lib/lv_bindings/lvgl/src/themes/lv_theme.c
+CC ../../lib/lv_bindings/lvgl/src/themes/mono/lv_theme_mono.c
+CC ../../lib/lv_bindings/lvgl/src/themes/simple/lv_theme_simple.c
+CC ../../lib/lv_bindings/lvgl/src/tick/lv_tick.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/3dtexture/lv_3dtexture.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/animimage/lv_animimage.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/arc/lv_arc.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/bar/lv_bar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/button/lv_button.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_chinese.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/canvas/lv_canvas.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/chart/lv_chart.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/checkbox/lv_checkbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/dropdown/lv_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/image/lv_image.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/imagebutton/lv_imagebutton.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/keyboard/lv_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/label/lv_label.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/led/lv_led.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/line/lv_line.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/list/lv_list.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/lottie/lv_lottie.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/menu/lv_menu.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/msgbox/lv_msgbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/objx_templ/lv_objx_templ.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_animimage_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_dropdown_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_image_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_keyboard_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_label_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_obj_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_roller_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_slider_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_style_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_textarea_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/roller/lv_roller.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/scale/lv_scale.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/slider/lv_slider.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/span/lv_span.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinbox/lv_spinbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinner/lv_spinner.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/switch/lv_switch.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/table/lv_table.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tabview/lv_tabview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/textarea/lv_textarea.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tileview/lv_tileview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/win/lv_win.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../lib/mbedtls_errors/mp_mbedtls_errors.c
+CC ../../lib/mbedtls/library/aes.c
+CC ../../lib/mbedtls/library/aesni.c
+CC ../../lib/mbedtls/library/asn1parse.c
+CC ../../lib/mbedtls/library/asn1write.c
+CC ../../lib/mbedtls/library/base64.c
+CC ../../lib/mbedtls/library/bignum_core.c
+CC ../../lib/mbedtls/library/bignum_mod.c
+CC ../../lib/mbedtls/library/bignum_mod_raw.c
+CC ../../lib/mbedtls/library/bignum.c
+CC ../../lib/mbedtls/library/camellia.c
+CC ../../lib/mbedtls/library/ccm.c
+CC ../../lib/mbedtls/library/chacha20.c
+CC ../../lib/mbedtls/library/chachapoly.c
+CC ../../lib/mbedtls/library/cipher.c
+CC ../../lib/mbedtls/library/cipher_wrap.c
+CC ../../lib/mbedtls/library/nist_kw.c
+CC ../../lib/mbedtls/library/aria.c
+CC ../../lib/mbedtls/library/cmac.c
+CC ../../lib/mbedtls/library/constant_time.c
+CC ../../lib/mbedtls/library/mps_reader.c
+CC ../../lib/mbedtls/library/mps_trace.c
+CC ../../lib/mbedtls/library/ctr_drbg.c
+CC ../../lib/mbedtls/library/debug.c
+CC ../../lib/mbedtls/library/des.c
+CC ../../lib/mbedtls/library/dhm.c
+CC ../../lib/mbedtls/library/ecdh.c
+CC ../../lib/mbedtls/library/ecdsa.c
+CC ../../lib/mbedtls/library/ecjpake.c
+CC ../../lib/mbedtls/library/ecp.c
+CC ../../lib/mbedtls/library/ecp_curves.c
+CC ../../lib/mbedtls/library/entropy.c
+CC ../../lib/mbedtls/library/entropy_poll.c
+CC ../../lib/mbedtls/library/gcm.c
+CC ../../lib/mbedtls/library/hmac_drbg.c
+CC ../../lib/mbedtls/library/md5.c
+CC ../../lib/mbedtls/library/md.c
+CC ../../lib/mbedtls/library/oid.c
+CC ../../lib/mbedtls/library/padlock.c
+CC ../../lib/mbedtls/library/pem.c
+CC ../../lib/mbedtls/library/pk.c
+CC ../../lib/mbedtls/library/pkcs12.c
+CC ../../lib/mbedtls/library/pkcs5.c
+CC ../../lib/mbedtls/library/pkparse.c
+CC ../../lib/mbedtls/library/pk_wrap.c
+CC ../../lib/mbedtls/library/pkwrite.c
+CC ../../lib/mbedtls/library/platform.c
+CC ../../lib/mbedtls/library/platform_util.c
+CC ../../lib/mbedtls/library/poly1305.c
+CC ../../lib/mbedtls/library/ripemd160.c
+CC ../../lib/mbedtls/library/rsa.c
+CC ../../lib/mbedtls/library/rsa_alt_helpers.c
+CC ../../lib/mbedtls/library/sha1.c
+CC ../../lib/mbedtls/library/sha256.c
+CC ../../lib/mbedtls/library/sha512.c
+CC ../../lib/mbedtls/library/ssl_cache.c
+CC ../../lib/mbedtls/library/ssl_ciphersuites.c
+CC ../../lib/mbedtls/library/ssl_client.c
+CC ../../lib/mbedtls/library/ssl_cookie.c
+CC ../../lib/mbedtls/library/ssl_debug_helpers_generated.c
+CC ../../lib/mbedtls/library/ssl_msg.c
+CC ../../lib/mbedtls/library/ssl_ticket.c
+CC ../../lib/mbedtls/library/ssl_tls.c
+CC ../../lib/mbedtls/library/ssl_tls12_client.c
+CC ../../lib/mbedtls/library/ssl_tls12_server.c
+CC ../../lib/mbedtls/library/timing.c
+CC ../../lib/mbedtls/library/x509.c
+CC ../../lib/mbedtls/library/x509_create.c
+CC ../../lib/mbedtls/library/x509_crl.c
+CC ../../lib/mbedtls/library/x509_crt.c
+CC ../../lib/mbedtls/library/x509_csr.c
+CC ../../lib/mbedtls/library/x509write_crt.c
+CC ../../lib/mbedtls/library/x509write_csr.c
+CC ../../shared/netutils/netutils.c
+CC ../../lib/lwip/src/apps/mdns/mdns.c
+CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
+CC ../../lib/lwip/src/apps/mdns/mdns_out.c
+CC ../../lib/lwip/src/core/def.c
+CC ../../lib/lwip/src/core/dns.c
+CC ../../lib/lwip/src/core/inet_chksum.c
+CC ../../lib/lwip/src/core/init.c
+CC ../../lib/lwip/src/core/ip.c
+CC ../../lib/lwip/src/core/mem.c
+CC ../../lib/lwip/src/core/memp.c
+CC ../../lib/lwip/src/core/netif.c
+CC ../../lib/lwip/src/core/pbuf.c
+CC ../../lib/lwip/src/core/raw.c
+CC ../../lib/lwip/src/core/stats.c
+CC ../../lib/lwip/src/core/sys.c
+CC ../../lib/lwip/src/core/tcp.c
+CC ../../lib/lwip/src/core/tcp_in.c
+CC ../../lib/lwip/src/core/tcp_out.c
+CC ../../lib/lwip/src/core/timeouts.c
+CC ../../lib/lwip/src/core/udp.c
+CC ../../lib/lwip/src/core/ipv4/acd.c
+CC ../../lib/lwip/src/core/ipv4/autoip.c
+CC ../../lib/lwip/src/core/ipv4/dhcp.c
+CC ../../lib/lwip/src/core/ipv4/etharp.c
+CC ../../lib/lwip/src/core/ipv4/icmp.c
+CC ../../lib/lwip/src/core/ipv4/igmp.c
+CC ../../lib/lwip/src/core/ipv4/ip4_addr.c
+CC ../../lib/lwip/src/core/ipv4/ip4.c
+CC ../../lib/lwip/src/core/ipv4/ip4_frag.c
+CC ../../lib/lwip/src/core/ipv6/dhcp6.c
+CC ../../lib/lwip/src/core/ipv6/ethip6.c
+CC ../../lib/lwip/src/core/ipv6/icmp6.c
+CC ../../lib/lwip/src/core/ipv6/inet6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_addr.c
+CC ../../lib/lwip/src/core/ipv6/ip6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_frag.c
+CC ../../lib/lwip/src/core/ipv6/mld6.c
+CC ../../lib/lwip/src/core/ipv6/nd6.c
+CC ../../lib/lwip/src/netif/ethernet.c
+CC ../../lib/lwip/src/netif/ppp/auth.c
+CC ../../lib/lwip/src/netif/ppp/ccp.c
+CC ../../lib/lwip/src/netif/ppp/chap-md5.c
+CC ../../lib/lwip/src/netif/ppp/chap_ms.c
+CC ../../lib/lwip/src/netif/ppp/chap-new.c
+CC ../../lib/lwip/src/netif/ppp/demand.c
+CC ../../lib/lwip/src/netif/ppp/eap.c
+CC ../../lib/lwip/src/netif/ppp/ecp.c
+CC ../../lib/lwip/src/netif/ppp/eui64.c
+CC ../../lib/lwip/src/netif/ppp/fsm.c
+CC ../../lib/lwip/src/netif/ppp/ipcp.c
+CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
+CC ../../lib/lwip/src/netif/ppp/lcp.c
+CC ../../lib/lwip/src/netif/ppp/magic.c
+CC ../../lib/lwip/src/netif/ppp/mppe.c
+CC ../../lib/lwip/src/netif/ppp/multilink.c
+CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
+CC ../../lib/lwip/src/netif/ppp/pppapi.c
+CC ../../lib/lwip/src/netif/ppp/ppp.c
+CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
+CC ../../lib/lwip/src/netif/ppp/pppoe.c
+CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
+CC ../../lib/lwip/src/netif/ppp/pppos.c
+CC ../../lib/lwip/src/netif/ppp/upap.c
+CC ../../lib/lwip/src/netif/ppp/utils.c
+CC ../../lib/lwip/src/netif/ppp/vj.c
+CC mbedtls/mbedtls_port.c
+CC ../../lib/libm/math.c
+CC ../../lib/libm/acoshf.c
+CC ../../lib/libm/asinfacosf.c
+CC ../../lib/libm/asinhf.c
+CC ../../lib/libm/atan2f.c
+CC ../../lib/libm/atanf.c
+CC ../../lib/libm/atanhf.c
+CC ../../lib/libm/ef_rem_pio2.c
+CC ../../lib/libm/erf_lgamma.c
+CC ../../lib/libm/fmodf.c
+CC ../../lib/libm/kf_cos.c
+CC ../../lib/libm/kf_rem_pio2.c
+CC ../../lib/libm/kf_sin.c
+CC ../../lib/libm/kf_tan.c
+CC ../../lib/libm/log1pf.c
+CC ../../lib/libm/nearbyintf.c
+CC ../../lib/libm/roundf.c
+CC ../../lib/libm/sf_cos.c
+CC ../../lib/libm/sf_erf.c
+CC ../../lib/libm/sf_frexp.c
+CC ../../lib/libm/sf_ldexp.c
+CC ../../lib/libm/sf_modf.c
+CC ../../lib/libm/sf_sin.c
+CC ../../lib/libm/sf_tan.c
+CC ../../lib/libm/wf_lgamma.c
+CC ../../lib/libm/wf_tgamma.c
+CC ../../lib/libm/thumb_vfp_sqrtf.c
+CC ../../shared/libc/string0.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_clocks.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_common.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_delay.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_guard.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_io.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_security.c
+CC ../../lib/fsp/ra/fsp/src/r_ioport/r_ioport.c
+CC ../../lib/fsp/ra/fsp/src/r_sci_uart/r_sci_uart.c
+CC ../../lib/fsp/ra/fsp/src/r_ospi/r_ospi.c
+CC ../../lib/fsp/ra/fsp/src/r_qspi/r_qspi.c
+CC ../../lib/fsp/ra/fsp/src/r_sdhi/r_sdhi.c
+CC ../../lib/fsp/ra/fsp/src/r_dtc/r_dtc.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/r_ether_phy_target_ics1894.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/r_ether_phy.c
+CC ../../lib/fsp/ra/fsp/src/r_ether/r_ether.c
+CC ../../lib/fsp/ra/fsp/src/r_lpm/r_lpm.c
+CC ../../lib/fsp/ra/fsp/src/r_flash_hp/r_flash_hp.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_ecc.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_sha.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_aes.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/r_sce_private.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p00.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p20.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p26.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p81.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p82.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p92.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p40.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func050.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func051.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func052.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func053.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func054.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func100.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func101.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func040.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func048.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func102.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func103.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_subprc01.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_hal.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/ra_gen/common_data.c
+CC boards/VK_RA6M5/ra_gen/hal_data.c
+CC boards/VK_RA6M5/ra_gen/pin_data.c
+CC boards/VK_RA6M5/ra_gen/vector_data.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c
+AS ../../shared/runtime/gchelper_thumb2.s
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+CC build-VK_RA6M5/frozen_content.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212238.log b/ports/renesas-ra/VK_RA6M5_build_202506212238.log
new file mode 100644
index 00000000..e7ece056
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212238.log
@@ -0,0 +1 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212239.log b/ports/renesas-ra/VK_RA6M5_build_202506212239.log
new file mode 100644
index 00000000..e7ece056
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212239.log
@@ -0,0 +1 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212345.log b/ports/renesas-ra/VK_RA6M5_build_202506212345.log
new file mode 100644
index 00000000..20beabbb
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212345.log
@@ -0,0 +1,76 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+../../py/gc_ospi.c: In function 'gc_ospi_stats_core':
+../../py/gc_ospi.c:275:5: error: unknown type name 'ospi_stat_t'
+  275 |     ospi_stat_t st;
+      |     ^~~~~~~~~~~
+../../py/gc_ospi.c:276:5: error: implicit declaration of function 'ospi_collect_stats'; did you mean 'gc_collect_start'? [-Werror=implicit-function-declaration]
+  276 |     ospi_collect_stats(a, &st);
+      |     ^~~~~~~~~~~~~~~~~~
+      |     gc_collect_start
+../../py/gc_ospi.c:278:21: error: request for member 'total_blocks' in something not a structure or union
+  278 |     size_t used = st.total_blocks - st.free_blocks;
+      |                     ^
+../../py/gc_ospi.c:278:39: error: request for member 'free_blocks' in something not a structure or union
+  278 |     size_t used = st.total_blocks - st.free_blocks;
+      |                                       ^
+../../py/gc_ospi.c:280:28: error: request for member 'free_blocks' in something not a structure or union
+  280 |     uint32_t frag_pct = (st.free_blocks > 0 && st.free_chunks > 1)
+      |                            ^
+../../py/gc_ospi.c:280:50: error: request for member 'free_chunks' in something not a structure or union
+  280 |     uint32_t frag_pct = (st.free_blocks > 0 && st.free_chunks > 1)
+      |                                                  ^
+../../py/gc_ospi.c:281:22: error: request for member 'free_chunks' in something not a structure or union
+  281 |         ? (100U * (st.free_chunks - 1)) / st.free_blocks
+      |                      ^
+../../py/gc_ospi.c:281:45: error: request for member 'free_blocks' in something not a structure or union
+  281 |         ? (100U * (st.free_chunks - 1)) / st.free_blocks
+      |                                             ^
+../../py/gc_ospi.c:291:21: error: request for member 'total_blocks' in something not a structure or union
+  291 |         (unsigned)st.total_blocks, (unsigned)(st.total_blocks*BYTES_PER_BLOCK/1024),
+      |                     ^
+../../py/gc_ospi.c:291:49: error: request for member 'total_blocks' in something not a structure or union
+  291 |         (unsigned)st.total_blocks, (unsigned)(st.total_blocks*BYTES_PER_BLOCK/1024),
+      |                                                 ^
+../../py/gc_ospi.c:293:21: error: request for member 'free_blocks' in something not a structure or union
+  293 |         (unsigned)st.free_blocks,   (unsigned)(st.free_blocks*BYTES_PER_BLOCK/1024),
+      |                     ^
+../../py/gc_ospi.c:293:50: error: request for member 'free_blocks' in something not a structure or union
+  293 |         (unsigned)st.free_blocks,   (unsigned)(st.free_blocks*BYTES_PER_BLOCK/1024),
+      |                                                  ^
+../../py/gc_ospi.c:294:21: error: request for member 'free_chunks' in something not a structure or union
+  294 |         (unsigned)st.free_chunks,   (unsigned)frag_pct,
+      |                     ^
+../../py/gc_ospi.c:295:21: error: request for member 'largest_free' in something not a structure or union
+  295 |         (unsigned)st.largest_free,  (unsigned)(st.largest_free*BYTES_PER_BLOCK/1024));
+      |                     ^
+../../py/gc_ospi.c:295:50: error: request for member 'largest_free' in something not a structure or union
+  295 |         (unsigned)st.largest_free,  (unsigned)(st.largest_free*BYTES_PER_BLOCK/1024));
+      |                                                  ^
+../../py/gc_ospi.c: At top level:
+../../py/gc_ospi.c:514:13: error: conflicting types for 'ospi_collect_stats' [-Werror]
+  514 | static void ospi_collect_stats(mp_state_mem_area_t *area, ospi_stat_t *st)
+      |             ^~~~~~~~~~~~~~~~~~
+../../py/gc_ospi.c:514:13: error: static declaration of 'ospi_collect_stats' follows non-static declaration
+../../py/gc_ospi.c:276:5: note: previous implicit declaration of 'ospi_collect_stats' was here
+  276 |     ospi_collect_stats(a, &st);
+      |     ^~~~~~~~~~~~~~~~~~
+../../py/gc_ospi.c:514:13: error: 'ospi_collect_stats' defined but not used [-Werror=unused-function]
+  514 | static void ospi_collect_stats(mp_state_mem_area_t *area, ospi_stat_t *st)
+      |             ^~~~~~~~~~~~~~~~~~
+cc1.exe: all warnings being treated as errors
+See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
+make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/py/gc_ospi.o] Error 1
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212349.log b/ports/renesas-ra/VK_RA6M5_build_202506212349.log
new file mode 100644
index 00000000..6f459a13
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212349.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506212355.log b/ports/renesas-ra/VK_RA6M5_build_202506212355.log
new file mode 100644
index 00000000..7f2de807
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506212355.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220002.log b/ports/renesas-ra/VK_RA6M5_build_202506220002.log
new file mode 100644
index 00000000..8673cb20
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220002.log
@@ -0,0 +1,28 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/modgc.c
+CC ../../py/modsys.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../shared/runtime/pyexec.c
+CC main.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220011.log b/ports/renesas-ra/VK_RA6M5_build_202506220011.log
new file mode 100644
index 00000000..7f2de807
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220011.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220013.log b/ports/renesas-ra/VK_RA6M5_build_202506220013.log
new file mode 100644
index 00000000..6f4cce8f
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220013.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC main.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220020.log b/ports/renesas-ra/VK_RA6M5_build_202506220020.log
new file mode 100644
index 00000000..4da1fa44
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220020.log
@@ -0,0 +1,21 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:64: error: "OSPI_START" redefined [-Werror]
+   64 | #define OSPI_START      0x68000000u
+      | 
+../../py/gc_ospi.c:34: note: this is the location of the previous definition
+   34 | #define OSPI_START 0x68000000
+      | 
+../../py/gc_ospi.c:65: error: "OSPI_END" redefined [-Werror]
+   65 | #define OSPI_END        0x68800000u
+      | 
+../../py/gc_ospi.c:35: note: this is the location of the previous definition
+   35 | #define OSPI_END   0x68800000
+      | 
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+cc1.exe: all warnings being treated as errors
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/gc_ospi.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220044.log b/ports/renesas-ra/VK_RA6M5_build_202506220044.log
new file mode 100644
index 00000000..f4491c51
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220044.log
@@ -0,0 +1,24 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/mpstate.c', '../../py/malloc.c', '../../py/gc_ospi.c', '../../py/modgc_ospi.c', '../../py/gc.c', '../../py/pystack.c', '../../py/qstr.c', '../../py/vstr.c', '../../py/mpprint.c', '../../py/unicode.c', '../../py/mpz.c', '../../py/reader.c', '../../py/lexer.c', '../../py/parse.c', '../../py/scope.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
+../../py/modmath.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../extmod/modbinascii.c:175: fatal error: when writing output to : Invalid argument
+  175 | 
+      | 
+compilation terminated.
+../../py/objtuple.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../extmod/machine_adc_block.c:29: fatal error: when writing output to : Invalid argument
+   29 | 
+      | 
+compilation terminated.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220055.log b/ports/renesas-ra/VK_RA6M5_build_202506220055.log
new file mode 100644
index 00000000..c46f46dd
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220055.log
@@ -0,0 +1,36 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/mpstate.c', '../../py/malloc.c', '../../py/gc_ospi.c', '../../py/modgc_ospi.c', '../../py/gc.c', '../../py/pystack.c', '../../py/qstr.c', '../../py/vstr.c', '../../py/mpprint.c', '../../py/unicode.c', '../../py/mpz.c', '../../py/reader.c', '../../py/lexer.c', '../../py/parse.c', '../../py/scope.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
+../../py/objcomplex.c:38: fatal error: when writing output to : Invalid argument
+   38 | 
+      | 
+compilation terminated.
+../../extmod/vfs_posix_file.c:32: fatal error: when writing output to : No such file or directory
+   32 | 
+      | 
+compilation terminated.
+../../py/objmap.c:31: fatal error: when writing output to : Invalid argument
+   31 | 
+      | 
+compilation terminated.
+../../extmod/network_cyw43.c:32: fatal error: when writing output to : Invalid argument
+   32 | 
+      | 
+compilation terminated.
+../../py/emitnxtensa.c:4: fatal error: when writing output to : No such file or directory
+    4 | 
+      | 
+compilation terminated.
+../../py/modmath.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../extmod/machine_adc_block.c:29: fatal error: when writing output to : Invalid argument
+   29 | 
+      | 
+compilation terminated.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220057.log b/ports/renesas-ra/VK_RA6M5_build_202506220057.log
new file mode 100644
index 00000000..b3c1f1f8
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220057.log
@@ -0,0 +1,28 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/mpstate.c', '../../py/malloc.c', '../../py/gc_ospi.c', '../../py/modgc_ospi.c', '../../py/gc.c', '../../py/pystack.c', '../../py/qstr.c', '../../py/vstr.c', '../../py/mpprint.c', '../../py/unicode.c', '../../py/mpz.c', '../../py/reader.c', '../../py/lexer.c', '../../py/parse.c', '../../py/scope.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
+../../py/objmodule.c:36: fatal error: when writing output to : Invalid argument
+   36 | 
+      | 
+compilation terminated.
+../../py/objtuple.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../extmod/vfs_reader.c:35: fatal error: when writing output to : Invalid argument
+   35 | 
+      | 
+compilation terminated.
+powerctrl.c:34: fatal error: when writing output to : Invalid argument
+   34 | 
+      | 
+compilation terminated.
+../../extmod/machine_adc_block.c:29: fatal error: when writing output to : Invalid argument
+   29 | 
+      | 
+compilation terminated.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220101.log b/ports/renesas-ra/VK_RA6M5_build_202506220101.log
new file mode 100644
index 00000000..b3263b9b
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220101.log
@@ -0,0 +1,40 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/mpstate.c', '../../py/malloc.c', '../../py/gc_ospi.c', '../../py/modgc_ospi.c', '../../py/gc.c', '../../py/pystack.c', '../../py/qstr.c', '../../py/vstr.c', '../../py/mpprint.c', '../../py/unicode.c', '../../py/mpz.c', '../../py/reader.c', '../../py/lexer.c', '../../py/parse.c', '../../py/scope.c']' returned non-zero exit status 1.
+../../py/objcomplex.c:38: fatal error: when writing output to : Invalid argument
+   38 | 
+      | 
+compilation terminated.
+../../extmod/network_cyw43.c:32: fatal error: when writing output to : Invalid argument
+   32 | 
+      | 
+compilation terminated.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
+machine_sdcard.c:35: fatal error: when writing output to : Invalid argument
+   35 | 
+      | 
+compilation terminated.
+build-VK_RA6M5/lvgl/lv_mpy.c:35: fatal error: when writing output to : Invalid argument
+   35 | 
+      | 
+compilation terminated.
+../../py/modmath.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../py/emitnxtensa.c:4: fatal error: when writing output to : No such file or directory
+    4 | 
+      | 
+compilation terminated.
+../../py/objtuple.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../extmod/machine_adc_block.c:29: fatal error: when writing output to : Invalid argument
+   29 | 
+      | 
+compilation terminated.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220102.log b/ports/renesas-ra/VK_RA6M5_build_202506220102.log
new file mode 100644
index 00000000..e172f312
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220102.log
@@ -0,0 +1,35 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/mpstate.c', '../../py/malloc.c', '../../py/gc_ospi.c', '../../py/modgc_ospi.c', '../../py/gc.c', '../../py/pystack.c', '../../py/qstr.c', '../../py/vstr.c', '../../py/mpprint.c', '../../py/unicode.c', '../../py/mpz.c', '../../py/reader.c', '../../py/lexer.c', '../../py/parse.c', '../../py/scope.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
+../../py/pairheap.c:28: fatal error: when writing output to : Invalid argument
+   28 | 
+      | 
+compilation terminated.
+../../py/objcomplex.c:38: fatal error: when writing output to : Invalid argument
+   38 | 
+      | 
+systick.c:36: fatal error: when writing output to : Invalid argument
+   36 | 
+      | 
+compilation terminated.
+compilation terminated.
+../../py/modmath.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+../../py/emitnxtensa.c:4: fatal error: when writing output to : No such file or directory
+    4 | 
+      | 
+compilation terminated.
+../../extmod/modbinascii.c:175: fatal error: when writing output to : Invalid argument
+  175 | 
+      | 
+network_lan.c:35: fatal error: when writing output to : Invalid argument
+   35 | 
+      | 
+compilation terminated.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220105.log b/ports/renesas-ra/VK_RA6M5_build_202506220105.log
new file mode 100644
index 00000000..9a8170c3
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220105.log
@@ -0,0 +1,36 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/mpstate.c', '../../py/malloc.c', '../../py/gc_ospi.c', '../../py/modgc_ospi.c', '../../py/gc.c', '../../py/pystack.c', '../../py/qstr.c', '../../py/vstr.c', '../../py/mpprint.c', '../../py/unicode.c', '../../py/mpz.c', '../../py/reader.c', '../../py/lexer.c', '../../py/parse.c', '../../py/scope.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
+../../extmod/vfs_posix_file.c:32: fatal error: when writing output to : No such file or directory
+   32 | 
+      | 
+compilation terminated.
+build-VK_RA6M5/lvgl/lv_mpy.c:35: fatal error: when writing output to : Invalid argument
+   35 | 
+      | 
+compilation terminated.
+../../py/modmath.c:33: fatal error: when writing output to : Invalid argument
+   33 | 
+      | 
+compilation terminated.
+network_lan.c:35: fatal error: when writing output to : Invalid argument
+   35 | 
+      | 
+compilation terminated.
+../../extmod/modbinascii.c:175: fatal error: when writing output to : Invalid argument
+  175 | 
+      | 
+compilation terminated.
+../../py/objcomplex.c:38: fatal error: when writing output to : Invalid argument
+   38 | 
+      | 
+compilation terminated.
+../../py/objmodule.c:36: fatal error: when writing output to : Invalid argument
+   36 | 
+      | 
+compilation terminated.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220107.log b/ports/renesas-ra/VK_RA6M5_build_202506220107.log
new file mode 100644
index 00000000..540bbb67
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220107.log
@@ -0,0 +1,24 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data not updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/modgc.c
+../../py/gc_ospi.c:417:15: error: 'get_allocation_size' defined but not used [-Werror=unused-function]
+  417 | static size_t get_allocation_size(mp_state_mem_area_t *area, size_t block) {
+      |               ^~~~~~~~~~~~~~~~~~~
+cc1.exe: all warnings being treated as errors
+See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
+make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/py/gc_ospi.o] Error 1
+make: *** Waiting for unfinished jobs....
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220109.log b/ports/renesas-ra/VK_RA6M5_build_202506220109.log
new file mode 100644
index 00000000..0b18f13b
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220109.log
@@ -0,0 +1,8 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+../../py/gc_ospi.c:10: error: unterminated #if
+   10 | #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+      | 
+Command '['arm-none-eabi-gcc', '-E', '-DCFG_TUH_MAX_SPEED=OPT_MODE_HIGH_SPEED', '-DCFG_TUD_MAX_SPEED=OPT_MODE_FULL_SPEED', '-DCFG_TUSB_RHPORT0_MODE=OPT_MODE_DEVICE', '-DCFG_TUSB_RHPORT1_MODE=0', '-DDEFAULT_DBG_CH=9', '-DMICROPY_PY_LVGL', '-DLV_COLOR_DEPTH=16', '-DLV_USE_TINY_TTF=0', '-DMICROPY_VFS_FAT=1', '-DMICROPY_PY_SSL=1', '-DMBEDTLS_CONFIG_FILE="mbedtls/mbedtls_config_port.h"', '-DMICROPY_SSL_MBEDTLS=1', '-I../../lib/mbedtls/include', '-DMICROPY_PY_LWIP=1', '-DFFCONF_H="lib/oofatfs/ffconf.h"', '-DRA6M5', '-DRA_HAL_H=<RA6M5_hal.h>', '-DRA_CFG_H=<vk_ra6m5_conf.h>', '-DCFG_TUSB_MCU=OPT_MCU_RAXXX', '-include', 'limits.h', '-I../..', '-I../../lib/lv_bindings', '-I../../lib/lwip/src/include', '-imacros', 'boards/compiler_barrier.h', '-I.', '-Ifsp_cfg', '-I../..', '-Ibuild-VK_RA6M5', '-I../../lib/cmsis/inc', '-I../../lib/fsp', '-I../../lib/fsp/ra/fsp/inc', '-I../../lib/fsp/ra/fsp/inc/api', '-I../../lib/fsp/ra/fsp/inc/instances', '-I../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Include', '-I../../lib/tinyusb/hw', '-I../../lib/tinyusb/src', '-I../../shared/tinyusb', '-Ilwip_inc', '-Ira', '-Iboards/VK_RA6M5/ra_gen', '-Iboards/VK_RA6M5/ra_cfg/driver', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg', '-Iboards/VK_RA6M5/ra_cfg/fsp_cfg/bsp', '-Idebug', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/api', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/inc', '-I../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//inc/instances', '-Wall', '-Wpointer-arith', '-Werror', '-Wdouble-promotion', '-Wfloat-conversion', '-std=gnu99', '-nostdlib', '-I../..', '-DSTATIC=static', '-mthumb', '-mfpu=fpv5-sp-d16', '-mfloat-abi=hard', '-mtune=cortex-m33', '-mcpu=cortex-m33', '-O3', '-DNDEBUG', '-Iboards/VK_RA6M5', '-DMICROPY_FLOAT_IMPL=MICROPY_FLOAT_IMPL_FLOAT', '-fsingle-precision-constant', '-fdata-sections', '-ffunction-sections', '-g', '-DUSE_FSP_OSPI', '-DUSE_FSP_QSPI', '-DUSE_FSP_SDHI', '-DUSE_FSP_ETH', '-DUSE_FSP_LPM', '-DUSE_FSP_FLASH', '-DMICROPY_ROM_TEXT_COMPRESSION=1', '-DNO_QSTR', '../../py/gc_ospi.c']' returned non-zero exit status 1.
+make: *** [../../py/mkrules.mk:122: build-VK_RA6M5/genhdr/qstr.i.last] Error 1
+make: *** Deleting file 'build-VK_RA6M5/genhdr/qstr.i.last'
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220111.log b/ports/renesas-ra/VK_RA6M5_build_202506220111.log
new file mode 100644
index 00000000..d3b7b07b
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220111.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data not updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+QSTR not updated
+Root pointer registrations not updated
+Module registrations not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220117.log b/ports/renesas-ra/VK_RA6M5_build_202506220117.log
new file mode 100644
index 00000000..22ea1d21
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220117.log
@@ -0,0 +1,21 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/modgc.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220127.log b/ports/renesas-ra/VK_RA6M5_build_202506220127.log
new file mode 100644
index 00000000..38ec4eb6
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220127.log
@@ -0,0 +1,9 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220128.log b/ports/renesas-ra/VK_RA6M5_build_202506220128.log
new file mode 100644
index 00000000..203aa19d
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220128.log
@@ -0,0 +1,11 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+Root pointer registrations not updated
+QSTR not updated
+Compressed data not updated
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220130.log b/ports/renesas-ra/VK_RA6M5_build_202506220130.log
new file mode 100644
index 00000000..e3259444
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220130.log
@@ -0,0 +1,1132 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+mkdir -p build-VK_RA6M5/genhdr
+LVGL-GEN build-VK_RA6M5/lvgl/lv_mpy.c
+GEN build-VK_RA6M5/pins_VK_RA6M5.c
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/ra_gen/
+mkdir -p build-VK_RA6M5/build-VK_RA6M5/lvgl/
+mkdir -p build-VK_RA6M5/drivers/bus/
+mkdir -p build-VK_RA6M5/drivers/dht/
+mkdir -p build-VK_RA6M5/drivers/memory/
+mkdir -p build-VK_RA6M5/extmod/
+mkdir -p build-VK_RA6M5/extmod/mbedtls/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/mcu/all/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_dtc/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_flash_hp/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ioport/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_lpm/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ospi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_qspi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sci_uart/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sdhi/
+mkdir -p build-VK_RA6M5/lib/libm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/core/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/display/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/dma2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nema_gfx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/g2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/pxp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/vglite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/opengles/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/renesas/dave2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/blend/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/vg_lite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/drm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/fb/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ft81x/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ili9341/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/lcd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7735/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7789/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7796/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/evdev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/glfw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/libinput/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/nuttx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/qnx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/wayland/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/windows/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/x11/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/font/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/indev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/flex/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/grid/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/barcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bin_decoder/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bmp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/expat/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/ffmpeg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/freetype/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/fsdrv/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/gif/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libpng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lodepng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lz4/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/qrcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rle/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rlottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/svg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tiny_ttf/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tjpgd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/class/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/instance/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/osal/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/file_explorer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/font_manager/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/fragment/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/gridnav/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/ime/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/imgfont/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/monkey/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/observer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/snapshot/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/sysmon/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/test/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/vg_lite_tvg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/parsers/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/builtin/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/clib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/micropython/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/rtthread/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/default/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/mono/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/simple/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/tick/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/3dtexture/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/animimage/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/arc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/bar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/button/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/buttonmatrix/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/calendar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/canvas/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/chart/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/checkbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/dropdown/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/image/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/imagebutton/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/keyboard/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/label/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/led/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/line/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/list/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/lottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/menu/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/msgbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/objx_templ/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/property/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/roller/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/scale/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/slider/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/span/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinner/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/switch/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/table/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tabview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/textarea/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tileview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/win/
+mkdir -p build-VK_RA6M5/lib/lwip/src/apps/mdns/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv4/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv6/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/polarssl/
+mkdir -p build-VK_RA6M5/lib/mbedtls/library/
+mkdir -p build-VK_RA6M5/lib/mbedtls_errors/
+mkdir -p build-VK_RA6M5/lib/oofatfs/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/cdc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/dfu/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/hid/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/midi/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/msc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/usbtmc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/vendor/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/common/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/device/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/portable/renesas/rusb2/
+mkdir -p build-VK_RA6M5/mbedtls/
+mkdir -p build-VK_RA6M5/py/
+mkdir -p build-VK_RA6M5/ra/
+mkdir -p build-VK_RA6M5/shared/libc/
+mkdir -p build-VK_RA6M5/shared/netutils/
+mkdir -p build-VK_RA6M5/shared/readline/
+mkdir -p build-VK_RA6M5/shared/runtime/
+mkdir -p build-VK_RA6M5/shared/timeutils/
+mkdir -p build-VK_RA6M5/shared/tinyusb/
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data updated
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.data.h
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+Module registrations updated
+Root pointer registrations updated
+GEN build-VK_RA6M5/genhdr/moduledefs.h
+GEN build-VK_RA6M5/genhdr/root_pointers.h
+QSTR updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+MPY asyncio/__init__.py
+MPY asyncio/core.py
+MPY asyncio/event.py
+MPY asyncio/funcs.py
+MPY asyncio/lock.py
+MPY asyncio/stream.py
+MPY uasyncio.py
+MPY dht.py
+MPY onewire.py
+GEN build-VK_RA6M5/frozen_content.c
+CC ../../extmod/machine_bitstream.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modopenamp.c
+CC ../../extmod/modopenamp_remoteproc.c
+CC ../../extmod/modopenamp_remoteproc_store.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/mbedtls/mbedtls_alt.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_group.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_class.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_draw.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_event.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_id_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_pos.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_property.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_tree.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_refr.c
+CC ../../lib/lv_bindings/lvgl/src/display/lv_display.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_3d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_image_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_stm32_hal.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_nema_gfx_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_buf_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_buf_map.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_osa.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/opengles/lv_draw_opengles.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sdl/lv_draw_sdl.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888_premultiplied.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565_swapped.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_letter.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_transform.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_math.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_pending.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/drm/lv_linux_drm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/fb/lv_linux_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ft81x/lv_ft81x.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ili9341/lv_ili9341.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7735/lv_st7735.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7789/lv_st7789.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7796/lv_st7796.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/lv_st_ltdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/evdev/lv_evdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_glfw_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_debug.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_driver.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_texture.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_libinput.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_xkb.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_entry.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_lcd.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_libuv.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_profiler.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/qnx/lv_qnx.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mouse.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mousewheel.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_private.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland_smm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_dmabuf.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer_axis.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_seat.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window_decorations.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_xdg_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_input.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_input.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_binfont_loader.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_fmt_txt.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_10.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_12.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14_aligned.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_18.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_20.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_22.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_24.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_26.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28_compressed.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_30.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_32.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_34.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_36.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_38.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_40.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_42.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_44.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_46.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_48.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_8.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_8.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/flex/lv_flex.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/grid/lv_grid.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/lv_layout.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/code128.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/lv_barcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bin_decoder/lv_bin_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bmp/lv_bmp.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlparse.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlrole.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_impl.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_ns.c
+CC ../../lib/lv_bindings/lvgl/src/libs/ffmpeg/lv_ffmpeg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_glyph.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_image.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_outline.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_ftsystem.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_cbfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_littlefs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_memfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_posix.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_stdio.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_win32.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/gifdec.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/lv_gif.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libpng/lv_libpng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lv_lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lz4/lz4.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/lv_qrcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/qrcodegen.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rle/lv_rle.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rlottie/lv_rlottie.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_parser.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_render.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_token.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/lv_tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/lv_init.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_header_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache_entry.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim_timeline.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_area.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_array.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_async.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_bidi.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_circle_buf.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color_op.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_event.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_fs.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_grad.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_iter.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_log.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_lru.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_math.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_palette.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_profiler_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_templ.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text_ap.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_timer.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_tree.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_utils.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_cmsis_rtos2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_freertos.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_linux.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_mqx.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os_none.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_pthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_sdl2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_windows.c
+CC ../../lib/lv_bindings/lvgl/src/others/file_explorer/lv_file_explorer.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager_recycle.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/gridnav/lv_gridnav.c
+CC ../../lib/lv_bindings/lvgl/src/others/ime/lv_ime_pinyin.c
+CC ../../lib/lv_bindings/lvgl/src/others/imgfont/lv_imgfont.c
+CC ../../lib/lv_bindings/lvgl/src/others/monkey/lv_monkey.c
+CC ../../lib/lv_bindings/lvgl/src/others/observer/lv_observer.c
+CC ../../lib/lv_bindings/lvgl/src/others/snapshot/lv_snapshot.c
+CC ../../lib/lv_bindings/lvgl/src/others/sysmon/lv_sysmon.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_display.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_helpers.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_screenshot_compare.c
+CC ../../lib/lv_bindings/lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_base_types.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_component.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_style.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_update.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_utils.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_widget.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_arc_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_bar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_buttonmatrix_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_button_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_calendar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_canvas_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_chart_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_checkbox_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_dropdown_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_event_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_image_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_keyboard_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_label_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_obj_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_roller_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_scale_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_slider_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_spangroup_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_table_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_tabview_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_textarea_parser.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_mem_core_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_sprintf_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_string_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_tlsf.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_mem_core_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_sprintf_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_string_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/lv_mem.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/micropython/lv_mem_core_micropython.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_string_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/uefi/lv_mem_core_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/themes/default/lv_theme_default.c
+CC ../../lib/lv_bindings/lvgl/src/themes/lv_theme.c
+CC ../../lib/lv_bindings/lvgl/src/themes/mono/lv_theme_mono.c
+CC ../../lib/lv_bindings/lvgl/src/themes/simple/lv_theme_simple.c
+CC ../../lib/lv_bindings/lvgl/src/tick/lv_tick.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/3dtexture/lv_3dtexture.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/animimage/lv_animimage.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/arc/lv_arc.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/bar/lv_bar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/button/lv_button.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_chinese.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/canvas/lv_canvas.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/chart/lv_chart.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/checkbox/lv_checkbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/dropdown/lv_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/image/lv_image.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/imagebutton/lv_imagebutton.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/keyboard/lv_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/label/lv_label.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/led/lv_led.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/line/lv_line.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/list/lv_list.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/lottie/lv_lottie.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/menu/lv_menu.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/msgbox/lv_msgbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/objx_templ/lv_objx_templ.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_animimage_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_dropdown_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_image_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_keyboard_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_label_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_obj_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_roller_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_slider_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_style_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_textarea_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/roller/lv_roller.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/scale/lv_scale.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/slider/lv_slider.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/span/lv_span.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinbox/lv_spinbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinner/lv_spinner.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/switch/lv_switch.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/table/lv_table.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tabview/lv_tabview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/textarea/lv_textarea.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tileview/lv_tileview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/win/lv_win.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../lib/mbedtls_errors/mp_mbedtls_errors.c
+CC ../../lib/mbedtls/library/aes.c
+CC ../../lib/mbedtls/library/aesni.c
+CC ../../lib/mbedtls/library/asn1parse.c
+CC ../../lib/mbedtls/library/asn1write.c
+CC ../../lib/mbedtls/library/base64.c
+CC ../../lib/mbedtls/library/bignum_core.c
+CC ../../lib/mbedtls/library/bignum_mod.c
+CC ../../lib/mbedtls/library/bignum_mod_raw.c
+CC ../../lib/mbedtls/library/bignum.c
+CC ../../lib/mbedtls/library/camellia.c
+CC ../../lib/mbedtls/library/ccm.c
+CC ../../lib/mbedtls/library/chacha20.c
+CC ../../lib/mbedtls/library/chachapoly.c
+CC ../../lib/mbedtls/library/cipher.c
+CC ../../lib/mbedtls/library/cipher_wrap.c
+CC ../../lib/mbedtls/library/nist_kw.c
+CC ../../lib/mbedtls/library/aria.c
+CC ../../lib/mbedtls/library/cmac.c
+CC ../../lib/mbedtls/library/constant_time.c
+CC ../../lib/mbedtls/library/mps_reader.c
+CC ../../lib/mbedtls/library/mps_trace.c
+CC ../../lib/mbedtls/library/ctr_drbg.c
+CC ../../lib/mbedtls/library/debug.c
+CC ../../lib/mbedtls/library/des.c
+CC ../../lib/mbedtls/library/dhm.c
+CC ../../lib/mbedtls/library/ecdh.c
+CC ../../lib/mbedtls/library/ecdsa.c
+CC ../../lib/mbedtls/library/ecjpake.c
+CC ../../lib/mbedtls/library/ecp.c
+CC ../../lib/mbedtls/library/ecp_curves.c
+CC ../../lib/mbedtls/library/entropy.c
+CC ../../lib/mbedtls/library/entropy_poll.c
+CC ../../lib/mbedtls/library/gcm.c
+CC ../../lib/mbedtls/library/hmac_drbg.c
+CC ../../lib/mbedtls/library/md5.c
+CC ../../lib/mbedtls/library/md.c
+CC ../../lib/mbedtls/library/oid.c
+CC ../../lib/mbedtls/library/padlock.c
+CC ../../lib/mbedtls/library/pem.c
+CC ../../lib/mbedtls/library/pk.c
+CC ../../lib/mbedtls/library/pkcs12.c
+CC ../../lib/mbedtls/library/pkcs5.c
+CC ../../lib/mbedtls/library/pkparse.c
+CC ../../lib/mbedtls/library/pk_wrap.c
+CC ../../lib/mbedtls/library/pkwrite.c
+CC ../../lib/mbedtls/library/platform.c
+CC ../../lib/mbedtls/library/platform_util.c
+CC ../../lib/mbedtls/library/poly1305.c
+CC ../../lib/mbedtls/library/ripemd160.c
+CC ../../lib/mbedtls/library/rsa.c
+CC ../../lib/mbedtls/library/rsa_alt_helpers.c
+CC ../../lib/mbedtls/library/sha1.c
+CC ../../lib/mbedtls/library/sha256.c
+CC ../../lib/mbedtls/library/sha512.c
+CC ../../lib/mbedtls/library/ssl_cache.c
+CC ../../lib/mbedtls/library/ssl_ciphersuites.c
+CC ../../lib/mbedtls/library/ssl_client.c
+CC ../../lib/mbedtls/library/ssl_cookie.c
+CC ../../lib/mbedtls/library/ssl_debug_helpers_generated.c
+CC ../../lib/mbedtls/library/ssl_msg.c
+CC ../../lib/mbedtls/library/ssl_ticket.c
+CC ../../lib/mbedtls/library/ssl_tls.c
+CC ../../lib/mbedtls/library/ssl_tls12_client.c
+CC ../../lib/mbedtls/library/ssl_tls12_server.c
+CC ../../lib/mbedtls/library/timing.c
+CC ../../lib/mbedtls/library/x509.c
+CC ../../lib/mbedtls/library/x509_create.c
+CC ../../lib/mbedtls/library/x509_crl.c
+CC ../../lib/mbedtls/library/x509_crt.c
+CC ../../lib/mbedtls/library/x509_csr.c
+CC ../../lib/mbedtls/library/x509write_crt.c
+CC ../../lib/mbedtls/library/x509write_csr.c
+CC ../../shared/netutils/netutils.c
+CC ../../lib/lwip/src/apps/mdns/mdns.c
+CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
+CC ../../lib/lwip/src/apps/mdns/mdns_out.c
+CC ../../lib/lwip/src/core/def.c
+CC ../../lib/lwip/src/core/dns.c
+CC ../../lib/lwip/src/core/inet_chksum.c
+CC ../../lib/lwip/src/core/init.c
+CC ../../lib/lwip/src/core/ip.c
+CC ../../lib/lwip/src/core/mem.c
+CC ../../lib/lwip/src/core/memp.c
+CC ../../lib/lwip/src/core/netif.c
+CC ../../lib/lwip/src/core/pbuf.c
+CC ../../lib/lwip/src/core/raw.c
+CC ../../lib/lwip/src/core/stats.c
+CC ../../lib/lwip/src/core/sys.c
+CC ../../lib/lwip/src/core/tcp.c
+CC ../../lib/lwip/src/core/tcp_in.c
+CC ../../lib/lwip/src/core/tcp_out.c
+CC ../../lib/lwip/src/core/timeouts.c
+CC ../../lib/lwip/src/core/udp.c
+CC ../../lib/lwip/src/core/ipv4/acd.c
+CC ../../lib/lwip/src/core/ipv4/autoip.c
+CC ../../lib/lwip/src/core/ipv4/dhcp.c
+CC ../../lib/lwip/src/core/ipv4/etharp.c
+CC ../../lib/lwip/src/core/ipv4/icmp.c
+CC ../../lib/lwip/src/core/ipv4/igmp.c
+CC ../../lib/lwip/src/core/ipv4/ip4_addr.c
+CC ../../lib/lwip/src/core/ipv4/ip4.c
+CC ../../lib/lwip/src/core/ipv4/ip4_frag.c
+CC ../../lib/lwip/src/core/ipv6/dhcp6.c
+CC ../../lib/lwip/src/core/ipv6/ethip6.c
+CC ../../lib/lwip/src/core/ipv6/icmp6.c
+CC ../../lib/lwip/src/core/ipv6/inet6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_addr.c
+CC ../../lib/lwip/src/core/ipv6/ip6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_frag.c
+CC ../../lib/lwip/src/core/ipv6/mld6.c
+CC ../../lib/lwip/src/core/ipv6/nd6.c
+CC ../../lib/lwip/src/netif/ethernet.c
+CC ../../lib/lwip/src/netif/ppp/auth.c
+CC ../../lib/lwip/src/netif/ppp/ccp.c
+CC ../../lib/lwip/src/netif/ppp/chap-md5.c
+CC ../../lib/lwip/src/netif/ppp/chap_ms.c
+CC ../../lib/lwip/src/netif/ppp/chap-new.c
+CC ../../lib/lwip/src/netif/ppp/demand.c
+CC ../../lib/lwip/src/netif/ppp/eap.c
+CC ../../lib/lwip/src/netif/ppp/ecp.c
+CC ../../lib/lwip/src/netif/ppp/eui64.c
+CC ../../lib/lwip/src/netif/ppp/fsm.c
+CC ../../lib/lwip/src/netif/ppp/ipcp.c
+CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
+CC ../../lib/lwip/src/netif/ppp/lcp.c
+CC ../../lib/lwip/src/netif/ppp/magic.c
+CC ../../lib/lwip/src/netif/ppp/mppe.c
+CC ../../lib/lwip/src/netif/ppp/multilink.c
+CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
+CC ../../lib/lwip/src/netif/ppp/pppapi.c
+CC ../../lib/lwip/src/netif/ppp/ppp.c
+CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
+CC ../../lib/lwip/src/netif/ppp/pppoe.c
+CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
+CC ../../lib/lwip/src/netif/ppp/pppos.c
+CC ../../lib/lwip/src/netif/ppp/upap.c
+CC ../../lib/lwip/src/netif/ppp/utils.c
+CC ../../lib/lwip/src/netif/ppp/vj.c
+CC mbedtls/mbedtls_port.c
+CC ../../lib/libm/math.c
+CC ../../lib/libm/acoshf.c
+CC ../../lib/libm/asinfacosf.c
+CC ../../lib/libm/asinhf.c
+CC ../../lib/libm/atan2f.c
+CC ../../lib/libm/atanf.c
+CC ../../lib/libm/atanhf.c
+CC ../../lib/libm/ef_rem_pio2.c
+CC ../../lib/libm/erf_lgamma.c
+CC ../../lib/libm/fmodf.c
+CC ../../lib/libm/kf_cos.c
+CC ../../lib/libm/kf_rem_pio2.c
+CC ../../lib/libm/kf_sin.c
+CC ../../lib/libm/kf_tan.c
+CC ../../lib/libm/log1pf.c
+CC ../../lib/libm/nearbyintf.c
+CC ../../lib/libm/roundf.c
+CC ../../lib/libm/sf_cos.c
+CC ../../lib/libm/sf_erf.c
+CC ../../lib/libm/sf_frexp.c
+CC ../../lib/libm/sf_ldexp.c
+CC ../../lib/libm/sf_modf.c
+CC ../../lib/libm/sf_sin.c
+CC ../../lib/libm/sf_tan.c
+CC ../../lib/libm/wf_lgamma.c
+CC ../../lib/libm/wf_tgamma.c
+CC ../../lib/libm/thumb_vfp_sqrtf.c
+CC ../../shared/libc/string0.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_clocks.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_common.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_delay.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_guard.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_io.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_security.c
+CC ../../lib/fsp/ra/fsp/src/r_ioport/r_ioport.c
+CC ../../lib/fsp/ra/fsp/src/r_sci_uart/r_sci_uart.c
+CC ../../lib/fsp/ra/fsp/src/r_ospi/r_ospi.c
+CC ../../lib/fsp/ra/fsp/src/r_qspi/r_qspi.c
+CC ../../lib/fsp/ra/fsp/src/r_sdhi/r_sdhi.c
+CC ../../lib/fsp/ra/fsp/src/r_dtc/r_dtc.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/r_ether_phy_target_ics1894.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/r_ether_phy.c
+CC ../../lib/fsp/ra/fsp/src/r_ether/r_ether.c
+CC ../../lib/fsp/ra/fsp/src/r_lpm/r_lpm.c
+CC ../../lib/fsp/ra/fsp/src/r_flash_hp/r_flash_hp.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_ecc.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_sha.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_aes.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/r_sce_private.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p00.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p20.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p26.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p81.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p82.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p92.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p40.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func050.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func051.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func052.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func053.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func054.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func100.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func101.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func040.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func048.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func102.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func103.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_subprc01.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_hal.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/ra_gen/common_data.c
+CC boards/VK_RA6M5/ra_gen/hal_data.c
+CC boards/VK_RA6M5/ra_gen/pin_data.c
+CC boards/VK_RA6M5/ra_gen/vector_data.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c
+AS ../../shared/runtime/gchelper_thumb2.s
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+CC build-VK_RA6M5/frozen_content.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220155.log b/ports/renesas-ra/VK_RA6M5_build_202506220155.log
new file mode 100644
index 00000000..6f459a13
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220155.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220206.log b/ports/renesas-ra/VK_RA6M5_build_202506220206.log
new file mode 100644
index 00000000..2e1f462f
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220206.log
@@ -0,0 +1,20 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ra/ra_init.c
+CC main.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220224.log b/ports/renesas-ra/VK_RA6M5_build_202506220224.log
new file mode 100644
index 00000000..d0734fd1
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220224.log
@@ -0,0 +1,24 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC ../../py/modsys.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../shared/runtime/pyexec.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506220240.log b/ports/renesas-ra/VK_RA6M5_build_202506220240.log
new file mode 100644
index 00000000..b8a568a0
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506220240.log
@@ -0,0 +1,1132 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+LVGL-GEN build-VK_RA6M5/lvgl/lv_mpy.c
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/ra_gen/
+mkdir -p build-VK_RA6M5/build-VK_RA6M5/lvgl/
+mkdir -p build-VK_RA6M5/drivers/bus/
+mkdir -p build-VK_RA6M5/drivers/dht/
+mkdir -p build-VK_RA6M5/drivers/memory/
+mkdir -p build-VK_RA6M5/extmod/
+mkdir -p build-VK_RA6M5/extmod/mbedtls/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/mcu/all/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_dtc/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_flash_hp/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ioport/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_lpm/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ospi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_qspi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sci_uart/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sdhi/
+mkdir -p build-VK_RA6M5/lib/libm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/core/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/display/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/dma2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nema_gfx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/g2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/pxp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/vglite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/opengles/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/renesas/dave2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/blend/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/vg_lite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/drm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/fb/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ft81x/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ili9341/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/lcd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7735/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7789/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7796/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/evdev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/glfw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/libinput/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/nuttx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/qnx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/wayland/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/windows/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/x11/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/font/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/indev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/flex/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/grid/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/barcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bin_decoder/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bmp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/expat/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/ffmpeg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/freetype/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/fsdrv/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/gif/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libpng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lodepng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lz4/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/qrcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rle/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rlottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/svg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tiny_ttf/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tjpgd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/class/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/instance/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/osal/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/file_explorer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/font_manager/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/fragment/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/gridnav/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/ime/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/imgfont/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/monkey/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/observer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/snapshot/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/sysmon/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/test/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/vg_lite_tvg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/parsers/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/builtin/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/clib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/micropython/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/rtthread/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/default/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/mono/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/simple/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/tick/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/3dtexture/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/animimage/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/arc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/bar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/button/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/buttonmatrix/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/calendar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/canvas/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/chart/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/checkbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/dropdown/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/image/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/imagebutton/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/keyboard/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/label/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/led/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/line/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/list/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/lottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/menu/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/msgbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/objx_templ/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/property/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/roller/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/scale/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/slider/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/span/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinner/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/switch/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/table/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tabview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/textarea/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tileview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/win/
+mkdir -p build-VK_RA6M5/lib/lwip/src/apps/mdns/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv4/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv6/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/polarssl/
+mkdir -p build-VK_RA6M5/lib/mbedtls/library/
+mkdir -p build-VK_RA6M5/lib/mbedtls_errors/
+mkdir -p build-VK_RA6M5/lib/oofatfs/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/cdc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/dfu/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/hid/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/midi/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/msc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/usbtmc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/vendor/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/common/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/device/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/portable/renesas/rusb2/
+mkdir -p build-VK_RA6M5/mbedtls/
+mkdir -p build-VK_RA6M5/py/
+mkdir -p build-VK_RA6M5/ra/
+mkdir -p build-VK_RA6M5/shared/libc/
+mkdir -p build-VK_RA6M5/shared/netutils/
+mkdir -p build-VK_RA6M5/shared/readline/
+mkdir -p build-VK_RA6M5/shared/runtime/
+mkdir -p build-VK_RA6M5/shared/timeutils/
+mkdir -p build-VK_RA6M5/shared/tinyusb/
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/compressed.data.h
+Module registrations updated
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+QSTR updated
+GEN build-VK_RA6M5/genhdr/moduledefs.h
+Root pointer registrations updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+GEN build-VK_RA6M5/genhdr/root_pointers.h
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+MPY asyncio/__init__.py
+MPY asyncio/core.py
+MPY asyncio/event.py
+MPY asyncio/funcs.py
+MPY asyncio/lock.py
+MPY asyncio/stream.py
+MPY uasyncio.py
+MPY dht.py
+MPY onewire.py
+GEN build-VK_RA6M5/frozen_content.c
+CC ../../extmod/machine_bitstream.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modopenamp.c
+CC ../../extmod/modopenamp_remoteproc.c
+CC ../../extmod/modopenamp_remoteproc_store.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/mbedtls/mbedtls_alt.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_group.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_class.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_draw.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_event.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_id_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_pos.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_property.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_tree.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_refr.c
+CC ../../lib/lv_bindings/lvgl/src/display/lv_display.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_3d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_image_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_stm32_hal.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_nema_gfx_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_buf_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_buf_map.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_osa.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/opengles/lv_draw_opengles.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sdl/lv_draw_sdl.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888_premultiplied.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565_swapped.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_letter.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_transform.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_math.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_pending.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/drm/lv_linux_drm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/fb/lv_linux_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ft81x/lv_ft81x.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ili9341/lv_ili9341.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7735/lv_st7735.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7789/lv_st7789.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7796/lv_st7796.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/lv_st_ltdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/evdev/lv_evdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_glfw_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_debug.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_driver.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_texture.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_libinput.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_xkb.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_entry.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_lcd.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_libuv.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_profiler.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/qnx/lv_qnx.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mouse.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mousewheel.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_private.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland_smm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_dmabuf.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer_axis.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_seat.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window_decorations.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_xdg_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_input.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_input.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_binfont_loader.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_fmt_txt.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_10.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_12.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14_aligned.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_18.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_20.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_22.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_24.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_26.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28_compressed.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_30.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_32.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_34.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_36.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_38.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_40.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_42.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_44.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_46.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_48.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_8.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_8.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/flex/lv_flex.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/grid/lv_grid.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/lv_layout.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/code128.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/lv_barcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bin_decoder/lv_bin_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bmp/lv_bmp.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlparse.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlrole.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_impl.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_ns.c
+CC ../../lib/lv_bindings/lvgl/src/libs/ffmpeg/lv_ffmpeg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_glyph.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_image.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_outline.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_ftsystem.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_cbfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_littlefs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_memfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_posix.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_stdio.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_win32.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/gifdec.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/lv_gif.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libpng/lv_libpng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lv_lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lz4/lz4.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/lv_qrcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/qrcodegen.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rle/lv_rle.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rlottie/lv_rlottie.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_parser.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_render.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_token.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/lv_tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/lv_init.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_header_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache_entry.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim_timeline.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_area.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_array.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_async.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_bidi.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_circle_buf.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color_op.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_event.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_fs.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_grad.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_iter.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_log.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_lru.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_math.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_palette.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_profiler_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_templ.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text_ap.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_timer.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_tree.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_utils.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_cmsis_rtos2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_freertos.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_linux.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_mqx.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os_none.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_pthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_sdl2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_windows.c
+CC ../../lib/lv_bindings/lvgl/src/others/file_explorer/lv_file_explorer.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager_recycle.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/gridnav/lv_gridnav.c
+CC ../../lib/lv_bindings/lvgl/src/others/ime/lv_ime_pinyin.c
+CC ../../lib/lv_bindings/lvgl/src/others/imgfont/lv_imgfont.c
+CC ../../lib/lv_bindings/lvgl/src/others/monkey/lv_monkey.c
+CC ../../lib/lv_bindings/lvgl/src/others/observer/lv_observer.c
+CC ../../lib/lv_bindings/lvgl/src/others/snapshot/lv_snapshot.c
+CC ../../lib/lv_bindings/lvgl/src/others/sysmon/lv_sysmon.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_display.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_helpers.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_screenshot_compare.c
+CC ../../lib/lv_bindings/lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_base_types.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_component.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_style.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_update.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_utils.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_widget.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_arc_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_bar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_buttonmatrix_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_button_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_calendar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_canvas_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_chart_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_checkbox_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_dropdown_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_event_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_image_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_keyboard_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_label_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_obj_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_roller_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_scale_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_slider_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_spangroup_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_table_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_tabview_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_textarea_parser.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_mem_core_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_sprintf_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_string_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_tlsf.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_mem_core_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_sprintf_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_string_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/lv_mem.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/micropython/lv_mem_core_micropython.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_string_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/uefi/lv_mem_core_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/themes/default/lv_theme_default.c
+CC ../../lib/lv_bindings/lvgl/src/themes/lv_theme.c
+CC ../../lib/lv_bindings/lvgl/src/themes/mono/lv_theme_mono.c
+CC ../../lib/lv_bindings/lvgl/src/themes/simple/lv_theme_simple.c
+CC ../../lib/lv_bindings/lvgl/src/tick/lv_tick.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/3dtexture/lv_3dtexture.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/animimage/lv_animimage.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/arc/lv_arc.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/bar/lv_bar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/button/lv_button.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_chinese.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/canvas/lv_canvas.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/chart/lv_chart.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/checkbox/lv_checkbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/dropdown/lv_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/image/lv_image.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/imagebutton/lv_imagebutton.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/keyboard/lv_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/label/lv_label.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/led/lv_led.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/line/lv_line.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/list/lv_list.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/lottie/lv_lottie.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/menu/lv_menu.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/msgbox/lv_msgbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/objx_templ/lv_objx_templ.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_animimage_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_dropdown_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_image_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_keyboard_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_label_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_obj_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_roller_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_slider_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_style_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_textarea_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/roller/lv_roller.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/scale/lv_scale.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/slider/lv_slider.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/span/lv_span.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinbox/lv_spinbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinner/lv_spinner.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/switch/lv_switch.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/table/lv_table.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tabview/lv_tabview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/textarea/lv_textarea.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tileview/lv_tileview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/win/lv_win.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../lib/mbedtls_errors/mp_mbedtls_errors.c
+CC ../../lib/mbedtls/library/aes.c
+CC ../../lib/mbedtls/library/aesni.c
+CC ../../lib/mbedtls/library/asn1parse.c
+CC ../../lib/mbedtls/library/asn1write.c
+CC ../../lib/mbedtls/library/base64.c
+CC ../../lib/mbedtls/library/bignum_core.c
+CC ../../lib/mbedtls/library/bignum_mod.c
+CC ../../lib/mbedtls/library/bignum_mod_raw.c
+CC ../../lib/mbedtls/library/bignum.c
+CC ../../lib/mbedtls/library/camellia.c
+CC ../../lib/mbedtls/library/ccm.c
+CC ../../lib/mbedtls/library/chacha20.c
+CC ../../lib/mbedtls/library/chachapoly.c
+CC ../../lib/mbedtls/library/cipher.c
+CC ../../lib/mbedtls/library/cipher_wrap.c
+CC ../../lib/mbedtls/library/nist_kw.c
+CC ../../lib/mbedtls/library/aria.c
+CC ../../lib/mbedtls/library/cmac.c
+CC ../../lib/mbedtls/library/constant_time.c
+CC ../../lib/mbedtls/library/mps_reader.c
+CC ../../lib/mbedtls/library/mps_trace.c
+CC ../../lib/mbedtls/library/ctr_drbg.c
+CC ../../lib/mbedtls/library/debug.c
+CC ../../lib/mbedtls/library/des.c
+CC ../../lib/mbedtls/library/dhm.c
+CC ../../lib/mbedtls/library/ecdh.c
+CC ../../lib/mbedtls/library/ecdsa.c
+CC ../../lib/mbedtls/library/ecjpake.c
+CC ../../lib/mbedtls/library/ecp.c
+CC ../../lib/mbedtls/library/ecp_curves.c
+CC ../../lib/mbedtls/library/entropy.c
+CC ../../lib/mbedtls/library/entropy_poll.c
+CC ../../lib/mbedtls/library/gcm.c
+CC ../../lib/mbedtls/library/hmac_drbg.c
+CC ../../lib/mbedtls/library/md5.c
+CC ../../lib/mbedtls/library/md.c
+CC ../../lib/mbedtls/library/oid.c
+CC ../../lib/mbedtls/library/padlock.c
+CC ../../lib/mbedtls/library/pem.c
+CC ../../lib/mbedtls/library/pk.c
+CC ../../lib/mbedtls/library/pkcs12.c
+CC ../../lib/mbedtls/library/pkcs5.c
+CC ../../lib/mbedtls/library/pkparse.c
+CC ../../lib/mbedtls/library/pk_wrap.c
+CC ../../lib/mbedtls/library/pkwrite.c
+CC ../../lib/mbedtls/library/platform.c
+CC ../../lib/mbedtls/library/platform_util.c
+CC ../../lib/mbedtls/library/poly1305.c
+CC ../../lib/mbedtls/library/ripemd160.c
+CC ../../lib/mbedtls/library/rsa.c
+CC ../../lib/mbedtls/library/rsa_alt_helpers.c
+CC ../../lib/mbedtls/library/sha1.c
+CC ../../lib/mbedtls/library/sha256.c
+CC ../../lib/mbedtls/library/sha512.c
+CC ../../lib/mbedtls/library/ssl_cache.c
+CC ../../lib/mbedtls/library/ssl_ciphersuites.c
+CC ../../lib/mbedtls/library/ssl_client.c
+CC ../../lib/mbedtls/library/ssl_cookie.c
+CC ../../lib/mbedtls/library/ssl_debug_helpers_generated.c
+CC ../../lib/mbedtls/library/ssl_msg.c
+CC ../../lib/mbedtls/library/ssl_ticket.c
+CC ../../lib/mbedtls/library/ssl_tls.c
+CC ../../lib/mbedtls/library/ssl_tls12_client.c
+CC ../../lib/mbedtls/library/ssl_tls12_server.c
+CC ../../lib/mbedtls/library/timing.c
+CC ../../lib/mbedtls/library/x509.c
+CC ../../lib/mbedtls/library/x509_create.c
+CC ../../lib/mbedtls/library/x509_crl.c
+CC ../../lib/mbedtls/library/x509_crt.c
+CC ../../lib/mbedtls/library/x509_csr.c
+CC ../../lib/mbedtls/library/x509write_crt.c
+CC ../../lib/mbedtls/library/x509write_csr.c
+CC ../../shared/netutils/netutils.c
+CC ../../lib/lwip/src/apps/mdns/mdns.c
+CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
+CC ../../lib/lwip/src/apps/mdns/mdns_out.c
+CC ../../lib/lwip/src/core/def.c
+CC ../../lib/lwip/src/core/dns.c
+CC ../../lib/lwip/src/core/inet_chksum.c
+CC ../../lib/lwip/src/core/init.c
+CC ../../lib/lwip/src/core/ip.c
+CC ../../lib/lwip/src/core/mem.c
+CC ../../lib/lwip/src/core/memp.c
+CC ../../lib/lwip/src/core/netif.c
+CC ../../lib/lwip/src/core/pbuf.c
+CC ../../lib/lwip/src/core/raw.c
+CC ../../lib/lwip/src/core/stats.c
+CC ../../lib/lwip/src/core/sys.c
+CC ../../lib/lwip/src/core/tcp.c
+CC ../../lib/lwip/src/core/tcp_in.c
+CC ../../lib/lwip/src/core/tcp_out.c
+CC ../../lib/lwip/src/core/timeouts.c
+CC ../../lib/lwip/src/core/udp.c
+CC ../../lib/lwip/src/core/ipv4/acd.c
+CC ../../lib/lwip/src/core/ipv4/autoip.c
+CC ../../lib/lwip/src/core/ipv4/dhcp.c
+CC ../../lib/lwip/src/core/ipv4/etharp.c
+CC ../../lib/lwip/src/core/ipv4/icmp.c
+CC ../../lib/lwip/src/core/ipv4/igmp.c
+CC ../../lib/lwip/src/core/ipv4/ip4_addr.c
+CC ../../lib/lwip/src/core/ipv4/ip4.c
+CC ../../lib/lwip/src/core/ipv4/ip4_frag.c
+CC ../../lib/lwip/src/core/ipv6/dhcp6.c
+CC ../../lib/lwip/src/core/ipv6/ethip6.c
+CC ../../lib/lwip/src/core/ipv6/icmp6.c
+CC ../../lib/lwip/src/core/ipv6/inet6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_addr.c
+CC ../../lib/lwip/src/core/ipv6/ip6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_frag.c
+CC ../../lib/lwip/src/core/ipv6/mld6.c
+CC ../../lib/lwip/src/core/ipv6/nd6.c
+CC ../../lib/lwip/src/netif/ethernet.c
+CC ../../lib/lwip/src/netif/ppp/auth.c
+CC ../../lib/lwip/src/netif/ppp/ccp.c
+CC ../../lib/lwip/src/netif/ppp/chap-md5.c
+CC ../../lib/lwip/src/netif/ppp/chap_ms.c
+CC ../../lib/lwip/src/netif/ppp/chap-new.c
+CC ../../lib/lwip/src/netif/ppp/demand.c
+CC ../../lib/lwip/src/netif/ppp/eap.c
+CC ../../lib/lwip/src/netif/ppp/ecp.c
+CC ../../lib/lwip/src/netif/ppp/eui64.c
+CC ../../lib/lwip/src/netif/ppp/fsm.c
+CC ../../lib/lwip/src/netif/ppp/ipcp.c
+CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
+CC ../../lib/lwip/src/netif/ppp/lcp.c
+CC ../../lib/lwip/src/netif/ppp/magic.c
+CC ../../lib/lwip/src/netif/ppp/mppe.c
+CC ../../lib/lwip/src/netif/ppp/multilink.c
+CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
+CC ../../lib/lwip/src/netif/ppp/pppapi.c
+CC ../../lib/lwip/src/netif/ppp/ppp.c
+CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
+CC ../../lib/lwip/src/netif/ppp/pppoe.c
+CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
+CC ../../lib/lwip/src/netif/ppp/pppos.c
+CC ../../lib/lwip/src/netif/ppp/upap.c
+CC ../../lib/lwip/src/netif/ppp/utils.c
+CC ../../lib/lwip/src/netif/ppp/vj.c
+CC mbedtls/mbedtls_port.c
+CC ../../lib/libm/math.c
+CC ../../lib/libm/acoshf.c
+CC ../../lib/libm/asinfacosf.c
+CC ../../lib/libm/asinhf.c
+CC ../../lib/libm/atan2f.c
+CC ../../lib/libm/atanf.c
+CC ../../lib/libm/atanhf.c
+CC ../../lib/libm/ef_rem_pio2.c
+CC ../../lib/libm/erf_lgamma.c
+CC ../../lib/libm/fmodf.c
+CC ../../lib/libm/kf_cos.c
+CC ../../lib/libm/kf_rem_pio2.c
+CC ../../lib/libm/kf_sin.c
+CC ../../lib/libm/kf_tan.c
+CC ../../lib/libm/log1pf.c
+CC ../../lib/libm/nearbyintf.c
+CC ../../lib/libm/roundf.c
+CC ../../lib/libm/sf_cos.c
+CC ../../lib/libm/sf_erf.c
+CC ../../lib/libm/sf_frexp.c
+CC ../../lib/libm/sf_ldexp.c
+CC ../../lib/libm/sf_modf.c
+CC ../../lib/libm/sf_sin.c
+CC ../../lib/libm/sf_tan.c
+CC ../../lib/libm/wf_lgamma.c
+CC ../../lib/libm/wf_tgamma.c
+CC ../../lib/libm/thumb_vfp_sqrtf.c
+CC ../../shared/libc/string0.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_clocks.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_common.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_delay.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_guard.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_io.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_security.c
+CC ../../lib/fsp/ra/fsp/src/r_ioport/r_ioport.c
+CC ../../lib/fsp/ra/fsp/src/r_sci_uart/r_sci_uart.c
+CC ../../lib/fsp/ra/fsp/src/r_ospi/r_ospi.c
+CC ../../lib/fsp/ra/fsp/src/r_qspi/r_qspi.c
+CC ../../lib/fsp/ra/fsp/src/r_sdhi/r_sdhi.c
+CC ../../lib/fsp/ra/fsp/src/r_dtc/r_dtc.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/r_ether_phy_target_ics1894.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/r_ether_phy.c
+CC ../../lib/fsp/ra/fsp/src/r_ether/r_ether.c
+CC ../../lib/fsp/ra/fsp/src/r_lpm/r_lpm.c
+CC ../../lib/fsp/ra/fsp/src/r_flash_hp/r_flash_hp.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_ecc.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_sha.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_aes.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/r_sce_private.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p00.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p20.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p26.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p81.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p82.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p92.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p40.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func050.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func051.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func052.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func053.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func054.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func100.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func101.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func040.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func048.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func102.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func103.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_subprc01.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_hal.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/board_init.c
+CC boards/VK_RA6M5/ra_gen/common_data.c
+CC boards/VK_RA6M5/ra_gen/hal_data.c
+CC boards/VK_RA6M5/ra_gen/pin_data.c
+CC boards/VK_RA6M5/ra_gen/vector_data.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c
+AS ../../shared/runtime/gchelper_thumb2.s
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+CC build-VK_RA6M5/frozen_content.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221003.log b/ports/renesas-ra/VK_RA6M5_build_202506221003.log
new file mode 100644
index 00000000..838c292e
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221003.log
@@ -0,0 +1,25 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC ../../py/modsys.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../shared/runtime/pyexec.c
+CC main.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221015.log b/ports/renesas-ra/VK_RA6M5_build_202506221015.log
new file mode 100644
index 00000000..7da93ee7
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221015.log
@@ -0,0 +1,9 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+Root pointer registrations not updated
+QSTR not updated
+Compressed data not updated
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221022.log b/ports/renesas-ra/VK_RA6M5_build_202506221022.log
new file mode 100644
index 00000000..6b8994cc
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221022.log
@@ -0,0 +1,23 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC boards/VK_RA6M5/board_init.c
+../../py/gc_ospi.c:246:14: error: 'ospi_alloc_from_area' defined but not used [-Werror=unused-function]
+  246 | static void *ospi_alloc_from_area(mp_state_mem_area_t *a,
+      |              ^~~~~~~~~~~~~~~~~~~~
+cc1.exe: all warnings being treated as errors
+See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
+make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/py/gc_ospi.o] Error 1
+make: *** Waiting for unfinished jobs....
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221024.log b/ports/renesas-ra/VK_RA6M5_build_202506221024.log
new file mode 100644
index 00000000..7f2de807
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221024.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221027.log b/ports/renesas-ra/VK_RA6M5_build_202506221027.log
new file mode 100644
index 00000000..34d9e1c4
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221027.log
@@ -0,0 +1,1134 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+mkdir -p build-VK_RA6M5/genhdr
+LVGL-GEN build-VK_RA6M5/lvgl/lv_mpy.c
+GEN build-VK_RA6M5/pins_VK_RA6M5.c
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/ra_gen/
+mkdir -p build-VK_RA6M5/build-VK_RA6M5/lvgl/
+mkdir -p build-VK_RA6M5/drivers/bus/
+mkdir -p build-VK_RA6M5/drivers/dht/
+mkdir -p build-VK_RA6M5/drivers/memory/
+mkdir -p build-VK_RA6M5/extmod/
+mkdir -p build-VK_RA6M5/extmod/mbedtls/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/mcu/all/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_dtc/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_flash_hp/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ioport/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_lpm/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ospi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_qspi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sci_uart/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sdhi/
+mkdir -p build-VK_RA6M5/lib/libm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/core/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/display/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/dma2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nema_gfx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/g2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/pxp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/vglite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/opengles/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/renesas/dave2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/blend/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/vg_lite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/drm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/fb/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ft81x/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ili9341/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/lcd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7735/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7789/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7796/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/evdev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/glfw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/libinput/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/nuttx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/qnx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/wayland/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/windows/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/x11/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/font/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/indev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/flex/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/grid/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/barcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bin_decoder/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bmp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/expat/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/ffmpeg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/freetype/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/fsdrv/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/gif/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libpng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lodepng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lz4/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/qrcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rle/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rlottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/svg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tiny_ttf/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tjpgd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/class/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/instance/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/osal/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/file_explorer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/font_manager/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/fragment/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/gridnav/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/ime/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/imgfont/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/monkey/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/observer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/snapshot/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/sysmon/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/test/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/vg_lite_tvg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/parsers/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/builtin/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/clib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/micropython/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/rtthread/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/default/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/mono/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/simple/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/tick/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/3dtexture/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/animimage/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/arc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/bar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/button/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/buttonmatrix/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/calendar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/canvas/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/chart/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/checkbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/dropdown/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/image/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/imagebutton/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/keyboard/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/label/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/led/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/line/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/list/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/lottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/menu/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/msgbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/objx_templ/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/property/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/roller/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/scale/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/slider/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/span/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinner/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/switch/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/table/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tabview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/textarea/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tileview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/win/
+mkdir -p build-VK_RA6M5/lib/lwip/src/apps/mdns/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv4/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv6/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/polarssl/
+mkdir -p build-VK_RA6M5/lib/mbedtls/library/
+mkdir -p build-VK_RA6M5/lib/mbedtls_errors/
+mkdir -p build-VK_RA6M5/lib/oofatfs/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/cdc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/dfu/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/hid/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/midi/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/msc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/usbtmc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/vendor/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/common/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/device/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/portable/renesas/rusb2/
+mkdir -p build-VK_RA6M5/mbedtls/
+mkdir -p build-VK_RA6M5/py/
+mkdir -p build-VK_RA6M5/ra/
+mkdir -p build-VK_RA6M5/shared/libc/
+mkdir -p build-VK_RA6M5/shared/netutils/
+mkdir -p build-VK_RA6M5/shared/readline/
+mkdir -p build-VK_RA6M5/shared/runtime/
+mkdir -p build-VK_RA6M5/shared/timeutils/
+mkdir -p build-VK_RA6M5/shared/tinyusb/
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data updated
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.data.h
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+Module registrations updated
+Root pointer registrations updated
+GEN build-VK_RA6M5/genhdr/moduledefs.h
+GEN build-VK_RA6M5/genhdr/root_pointers.h
+QSTR updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+MPY asyncio/__init__.py
+MPY asyncio/core.py
+MPY asyncio/event.py
+MPY asyncio/funcs.py
+MPY asyncio/lock.py
+MPY asyncio/stream.py
+MPY uasyncio.py
+MPY dht.py
+MPY onewire.py
+GEN build-VK_RA6M5/frozen_content.c
+CC ../../extmod/machine_bitstream.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modopenamp.c
+CC ../../extmod/modopenamp_remoteproc.c
+CC ../../extmod/modopenamp_remoteproc_store.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/mbedtls/mbedtls_alt.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_group.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_class.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_draw.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_event.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_id_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_pos.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_property.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_tree.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_refr.c
+CC ../../lib/lv_bindings/lvgl/src/display/lv_display.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_3d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_image_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_stm32_hal.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_nema_gfx_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_buf_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_buf_map.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_osa.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/opengles/lv_draw_opengles.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sdl/lv_draw_sdl.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888_premultiplied.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565_swapped.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_letter.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_transform.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_math.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_pending.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/drm/lv_linux_drm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/fb/lv_linux_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ft81x/lv_ft81x.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ili9341/lv_ili9341.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7735/lv_st7735.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7789/lv_st7789.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7796/lv_st7796.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/lv_st_ltdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/evdev/lv_evdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_glfw_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_debug.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_driver.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_texture.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_libinput.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_xkb.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_entry.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_lcd.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_libuv.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_profiler.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/qnx/lv_qnx.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mouse.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mousewheel.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_private.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland_smm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_dmabuf.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer_axis.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_seat.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window_decorations.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_xdg_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_input.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_input.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_binfont_loader.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_fmt_txt.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_10.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_12.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14_aligned.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_18.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_20.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_22.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_24.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_26.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28_compressed.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_30.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_32.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_34.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_36.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_38.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_40.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_42.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_44.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_46.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_48.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_8.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_8.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/flex/lv_flex.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/grid/lv_grid.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/lv_layout.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/code128.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/lv_barcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bin_decoder/lv_bin_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bmp/lv_bmp.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlparse.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlrole.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_impl.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_ns.c
+CC ../../lib/lv_bindings/lvgl/src/libs/ffmpeg/lv_ffmpeg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_glyph.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_image.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_outline.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_ftsystem.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_cbfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_littlefs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_memfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_posix.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_stdio.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_win32.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/gifdec.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/lv_gif.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libpng/lv_libpng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lv_lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lz4/lz4.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/lv_qrcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/qrcodegen.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rle/lv_rle.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rlottie/lv_rlottie.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_parser.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_render.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_token.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/lv_tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/lv_init.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_header_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache_entry.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim_timeline.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_area.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_array.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_async.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_bidi.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_circle_buf.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color_op.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_event.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_fs.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_grad.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_iter.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_log.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_lru.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_math.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_palette.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_profiler_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_templ.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text_ap.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_timer.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_tree.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_utils.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_cmsis_rtos2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_freertos.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_linux.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_mqx.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os_none.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_pthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_sdl2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_windows.c
+CC ../../lib/lv_bindings/lvgl/src/others/file_explorer/lv_file_explorer.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager_recycle.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/gridnav/lv_gridnav.c
+CC ../../lib/lv_bindings/lvgl/src/others/ime/lv_ime_pinyin.c
+CC ../../lib/lv_bindings/lvgl/src/others/imgfont/lv_imgfont.c
+CC ../../lib/lv_bindings/lvgl/src/others/monkey/lv_monkey.c
+CC ../../lib/lv_bindings/lvgl/src/others/observer/lv_observer.c
+CC ../../lib/lv_bindings/lvgl/src/others/snapshot/lv_snapshot.c
+CC ../../lib/lv_bindings/lvgl/src/others/sysmon/lv_sysmon.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_display.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_helpers.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_screenshot_compare.c
+CC ../../lib/lv_bindings/lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_base_types.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_component.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_style.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_update.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_utils.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_widget.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_arc_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_bar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_buttonmatrix_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_button_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_calendar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_canvas_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_chart_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_checkbox_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_dropdown_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_event_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_image_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_keyboard_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_label_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_obj_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_roller_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_scale_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_slider_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_spangroup_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_table_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_tabview_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_textarea_parser.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_mem_core_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_sprintf_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_string_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_tlsf.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_mem_core_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_sprintf_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_string_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/lv_mem.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/micropython/lv_mem_core_micropython.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_string_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/uefi/lv_mem_core_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/themes/default/lv_theme_default.c
+CC ../../lib/lv_bindings/lvgl/src/themes/lv_theme.c
+CC ../../lib/lv_bindings/lvgl/src/themes/mono/lv_theme_mono.c
+CC ../../lib/lv_bindings/lvgl/src/themes/simple/lv_theme_simple.c
+CC ../../lib/lv_bindings/lvgl/src/tick/lv_tick.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/3dtexture/lv_3dtexture.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/animimage/lv_animimage.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/arc/lv_arc.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/bar/lv_bar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/button/lv_button.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_chinese.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/canvas/lv_canvas.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/chart/lv_chart.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/checkbox/lv_checkbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/dropdown/lv_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/image/lv_image.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/imagebutton/lv_imagebutton.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/keyboard/lv_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/label/lv_label.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/led/lv_led.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/line/lv_line.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/list/lv_list.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/lottie/lv_lottie.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/menu/lv_menu.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/msgbox/lv_msgbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/objx_templ/lv_objx_templ.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_animimage_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_dropdown_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_image_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_keyboard_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_label_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_obj_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_roller_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_slider_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_style_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_textarea_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/roller/lv_roller.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/scale/lv_scale.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/slider/lv_slider.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/span/lv_span.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinbox/lv_spinbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinner/lv_spinner.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/switch/lv_switch.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/table/lv_table.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tabview/lv_tabview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/textarea/lv_textarea.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tileview/lv_tileview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/win/lv_win.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../lib/mbedtls_errors/mp_mbedtls_errors.c
+CC ../../lib/mbedtls/library/aes.c
+CC ../../lib/mbedtls/library/aesni.c
+CC ../../lib/mbedtls/library/asn1parse.c
+CC ../../lib/mbedtls/library/asn1write.c
+CC ../../lib/mbedtls/library/base64.c
+CC ../../lib/mbedtls/library/bignum_core.c
+CC ../../lib/mbedtls/library/bignum_mod.c
+CC ../../lib/mbedtls/library/bignum_mod_raw.c
+CC ../../lib/mbedtls/library/bignum.c
+CC ../../lib/mbedtls/library/camellia.c
+CC ../../lib/mbedtls/library/ccm.c
+CC ../../lib/mbedtls/library/chacha20.c
+CC ../../lib/mbedtls/library/chachapoly.c
+CC ../../lib/mbedtls/library/cipher.c
+CC ../../lib/mbedtls/library/cipher_wrap.c
+CC ../../lib/mbedtls/library/nist_kw.c
+CC ../../lib/mbedtls/library/aria.c
+CC ../../lib/mbedtls/library/cmac.c
+CC ../../lib/mbedtls/library/constant_time.c
+CC ../../lib/mbedtls/library/mps_reader.c
+CC ../../lib/mbedtls/library/mps_trace.c
+CC ../../lib/mbedtls/library/ctr_drbg.c
+CC ../../lib/mbedtls/library/debug.c
+CC ../../lib/mbedtls/library/des.c
+CC ../../lib/mbedtls/library/dhm.c
+CC ../../lib/mbedtls/library/ecdh.c
+CC ../../lib/mbedtls/library/ecdsa.c
+CC ../../lib/mbedtls/library/ecjpake.c
+CC ../../lib/mbedtls/library/ecp.c
+CC ../../lib/mbedtls/library/ecp_curves.c
+CC ../../lib/mbedtls/library/entropy.c
+CC ../../lib/mbedtls/library/entropy_poll.c
+CC ../../lib/mbedtls/library/gcm.c
+CC ../../lib/mbedtls/library/hmac_drbg.c
+CC ../../lib/mbedtls/library/md5.c
+CC ../../lib/mbedtls/library/md.c
+CC ../../lib/mbedtls/library/oid.c
+CC ../../lib/mbedtls/library/padlock.c
+CC ../../lib/mbedtls/library/pem.c
+CC ../../lib/mbedtls/library/pk.c
+CC ../../lib/mbedtls/library/pkcs12.c
+CC ../../lib/mbedtls/library/pkcs5.c
+CC ../../lib/mbedtls/library/pkparse.c
+CC ../../lib/mbedtls/library/pk_wrap.c
+CC ../../lib/mbedtls/library/pkwrite.c
+CC ../../lib/mbedtls/library/platform.c
+CC ../../lib/mbedtls/library/platform_util.c
+CC ../../lib/mbedtls/library/poly1305.c
+CC ../../lib/mbedtls/library/ripemd160.c
+CC ../../lib/mbedtls/library/rsa.c
+CC ../../lib/mbedtls/library/rsa_alt_helpers.c
+CC ../../lib/mbedtls/library/sha1.c
+CC ../../lib/mbedtls/library/sha256.c
+CC ../../lib/mbedtls/library/sha512.c
+CC ../../lib/mbedtls/library/ssl_cache.c
+CC ../../lib/mbedtls/library/ssl_ciphersuites.c
+CC ../../lib/mbedtls/library/ssl_client.c
+CC ../../lib/mbedtls/library/ssl_cookie.c
+CC ../../lib/mbedtls/library/ssl_debug_helpers_generated.c
+CC ../../lib/mbedtls/library/ssl_msg.c
+CC ../../lib/mbedtls/library/ssl_ticket.c
+CC ../../lib/mbedtls/library/ssl_tls.c
+CC ../../lib/mbedtls/library/ssl_tls12_client.c
+CC ../../lib/mbedtls/library/ssl_tls12_server.c
+CC ../../lib/mbedtls/library/timing.c
+CC ../../lib/mbedtls/library/x509.c
+CC ../../lib/mbedtls/library/x509_create.c
+CC ../../lib/mbedtls/library/x509_crl.c
+CC ../../lib/mbedtls/library/x509_crt.c
+CC ../../lib/mbedtls/library/x509_csr.c
+CC ../../lib/mbedtls/library/x509write_crt.c
+CC ../../lib/mbedtls/library/x509write_csr.c
+CC ../../shared/netutils/netutils.c
+CC ../../lib/lwip/src/apps/mdns/mdns.c
+CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
+CC ../../lib/lwip/src/apps/mdns/mdns_out.c
+CC ../../lib/lwip/src/core/def.c
+CC ../../lib/lwip/src/core/dns.c
+CC ../../lib/lwip/src/core/inet_chksum.c
+CC ../../lib/lwip/src/core/init.c
+CC ../../lib/lwip/src/core/ip.c
+CC ../../lib/lwip/src/core/mem.c
+CC ../../lib/lwip/src/core/memp.c
+CC ../../lib/lwip/src/core/netif.c
+CC ../../lib/lwip/src/core/pbuf.c
+CC ../../lib/lwip/src/core/raw.c
+CC ../../lib/lwip/src/core/stats.c
+CC ../../lib/lwip/src/core/sys.c
+CC ../../lib/lwip/src/core/tcp.c
+CC ../../lib/lwip/src/core/tcp_in.c
+CC ../../lib/lwip/src/core/tcp_out.c
+CC ../../lib/lwip/src/core/timeouts.c
+CC ../../lib/lwip/src/core/udp.c
+CC ../../lib/lwip/src/core/ipv4/acd.c
+CC ../../lib/lwip/src/core/ipv4/autoip.c
+CC ../../lib/lwip/src/core/ipv4/dhcp.c
+CC ../../lib/lwip/src/core/ipv4/etharp.c
+CC ../../lib/lwip/src/core/ipv4/icmp.c
+CC ../../lib/lwip/src/core/ipv4/igmp.c
+CC ../../lib/lwip/src/core/ipv4/ip4_addr.c
+CC ../../lib/lwip/src/core/ipv4/ip4.c
+CC ../../lib/lwip/src/core/ipv4/ip4_frag.c
+CC ../../lib/lwip/src/core/ipv6/dhcp6.c
+CC ../../lib/lwip/src/core/ipv6/ethip6.c
+CC ../../lib/lwip/src/core/ipv6/icmp6.c
+CC ../../lib/lwip/src/core/ipv6/inet6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_addr.c
+CC ../../lib/lwip/src/core/ipv6/ip6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_frag.c
+CC ../../lib/lwip/src/core/ipv6/mld6.c
+CC ../../lib/lwip/src/core/ipv6/nd6.c
+CC ../../lib/lwip/src/netif/ethernet.c
+CC ../../lib/lwip/src/netif/ppp/auth.c
+CC ../../lib/lwip/src/netif/ppp/ccp.c
+CC ../../lib/lwip/src/netif/ppp/chap-md5.c
+CC ../../lib/lwip/src/netif/ppp/chap_ms.c
+CC ../../lib/lwip/src/netif/ppp/chap-new.c
+CC ../../lib/lwip/src/netif/ppp/demand.c
+CC ../../lib/lwip/src/netif/ppp/eap.c
+CC ../../lib/lwip/src/netif/ppp/ecp.c
+CC ../../lib/lwip/src/netif/ppp/eui64.c
+CC ../../lib/lwip/src/netif/ppp/fsm.c
+CC ../../lib/lwip/src/netif/ppp/ipcp.c
+CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
+CC ../../lib/lwip/src/netif/ppp/lcp.c
+CC ../../lib/lwip/src/netif/ppp/magic.c
+CC ../../lib/lwip/src/netif/ppp/mppe.c
+CC ../../lib/lwip/src/netif/ppp/multilink.c
+CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
+CC ../../lib/lwip/src/netif/ppp/pppapi.c
+CC ../../lib/lwip/src/netif/ppp/ppp.c
+CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
+CC ../../lib/lwip/src/netif/ppp/pppoe.c
+CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
+CC ../../lib/lwip/src/netif/ppp/pppos.c
+CC ../../lib/lwip/src/netif/ppp/upap.c
+CC ../../lib/lwip/src/netif/ppp/utils.c
+CC ../../lib/lwip/src/netif/ppp/vj.c
+CC mbedtls/mbedtls_port.c
+CC ../../lib/libm/math.c
+CC ../../lib/libm/acoshf.c
+CC ../../lib/libm/asinfacosf.c
+CC ../../lib/libm/asinhf.c
+CC ../../lib/libm/atan2f.c
+CC ../../lib/libm/atanf.c
+CC ../../lib/libm/atanhf.c
+CC ../../lib/libm/ef_rem_pio2.c
+CC ../../lib/libm/erf_lgamma.c
+CC ../../lib/libm/fmodf.c
+CC ../../lib/libm/kf_cos.c
+CC ../../lib/libm/kf_rem_pio2.c
+CC ../../lib/libm/kf_sin.c
+CC ../../lib/libm/kf_tan.c
+CC ../../lib/libm/log1pf.c
+CC ../../lib/libm/nearbyintf.c
+CC ../../lib/libm/roundf.c
+CC ../../lib/libm/sf_cos.c
+CC ../../lib/libm/sf_erf.c
+CC ../../lib/libm/sf_frexp.c
+CC ../../lib/libm/sf_ldexp.c
+CC ../../lib/libm/sf_modf.c
+CC ../../lib/libm/sf_sin.c
+CC ../../lib/libm/sf_tan.c
+CC ../../lib/libm/wf_lgamma.c
+CC ../../lib/libm/wf_tgamma.c
+CC ../../lib/libm/thumb_vfp_sqrtf.c
+CC ../../shared/libc/string0.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_clocks.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_common.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_delay.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_guard.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_io.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_security.c
+CC ../../lib/fsp/ra/fsp/src/r_ioport/r_ioport.c
+CC ../../lib/fsp/ra/fsp/src/r_sci_uart/r_sci_uart.c
+CC ../../lib/fsp/ra/fsp/src/r_ospi/r_ospi.c
+CC ../../lib/fsp/ra/fsp/src/r_qspi/r_qspi.c
+CC ../../lib/fsp/ra/fsp/src/r_sdhi/r_sdhi.c
+CC ../../lib/fsp/ra/fsp/src/r_dtc/r_dtc.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/r_ether_phy_target_ics1894.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/r_ether_phy.c
+CC ../../lib/fsp/ra/fsp/src/r_ether/r_ether.c
+CC ../../lib/fsp/ra/fsp/src/r_lpm/r_lpm.c
+CC ../../lib/fsp/ra/fsp/src/r_flash_hp/r_flash_hp.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_ecc.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_sha.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_aes.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/r_sce_private.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p00.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p20.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p26.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p81.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p82.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p92.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p40.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func050.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func051.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func052.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func053.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func054.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func100.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func101.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func040.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func048.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func102.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func103.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_subprc01.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_hal.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/board_init.c
+CC boards/VK_RA6M5/ra_gen/common_data.c
+CC boards/VK_RA6M5/ra_gen/hal_data.c
+CC boards/VK_RA6M5/ra_gen/pin_data.c
+CC boards/VK_RA6M5/ra_gen/vector_data.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c
+AS ../../shared/runtime/gchelper_thumb2.s
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+CC build-VK_RA6M5/frozen_content.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221057.log b/ports/renesas-ra/VK_RA6M5_build_202506221057.log
new file mode 100644
index 00000000..e7ece056
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221057.log
@@ -0,0 +1 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221103.log b/ports/renesas-ra/VK_RA6M5_build_202506221103.log
new file mode 100644
index 00000000..e7ece056
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221103.log
@@ -0,0 +1 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221104.log b/ports/renesas-ra/VK_RA6M5_build_202506221104.log
new file mode 100644
index 00000000..e7ece056
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221104.log
@@ -0,0 +1 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221105.log b/ports/renesas-ra/VK_RA6M5_build_202506221105.log
new file mode 100644
index 00000000..fb3ec29a
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221105.log
@@ -0,0 +1,3 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221106.log b/ports/renesas-ra/VK_RA6M5_build_202506221106.log
new file mode 100644
index 00000000..f2766354
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221106.log
@@ -0,0 +1,1136 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+rm -rf build-VK_RA6M5 
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+mkdir -p build-VK_RA6M5/genhdr
+GEN build-VK_RA6M5/genhdr/pins.h
+LVGL-GEN build-VK_RA6M5/lvgl/lv_mpy.c
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+QSTR updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+Module registrations updated
+GEN build-VK_RA6M5/genhdr/moduledefs.h
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+Root pointer registrations updated
+GEN build-VK_RA6M5/genhdr/root_pointers.h
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Compressed data updated
+GEN build-VK_RA6M5/genhdr/compressed.data.h
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/ra_gen/
+mkdir -p build-VK_RA6M5/build-VK_RA6M5/lvgl/
+mkdir -p build-VK_RA6M5/drivers/bus/
+mkdir -p build-VK_RA6M5/drivers/dht/
+mkdir -p build-VK_RA6M5/drivers/memory/
+mkdir -p build-VK_RA6M5/extmod/
+mkdir -p build-VK_RA6M5/extmod/mbedtls/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/mcu/all/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_dtc/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_flash_hp/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ioport/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_lpm/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ospi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_qspi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sci_uart/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sdhi/
+mkdir -p build-VK_RA6M5/lib/libm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/core/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/display/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/dma2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nema_gfx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/g2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/pxp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/vglite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/opengles/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/renesas/dave2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/blend/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/vg_lite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/drm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/fb/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ft81x/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ili9341/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/lcd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7735/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7789/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7796/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/evdev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/glfw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/libinput/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/nuttx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/qnx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/wayland/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/windows/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/x11/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/font/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/indev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/flex/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/grid/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/barcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bin_decoder/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bmp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/expat/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/ffmpeg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/freetype/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/fsdrv/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/gif/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libpng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lodepng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lz4/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/qrcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rle/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rlottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/svg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tiny_ttf/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tjpgd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/class/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/instance/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/osal/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/file_explorer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/font_manager/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/fragment/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/gridnav/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/ime/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/imgfont/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/monkey/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/observer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/snapshot/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/sysmon/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/test/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/vg_lite_tvg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/parsers/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/builtin/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/clib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/micropython/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/rtthread/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/default/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/mono/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/simple/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/tick/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/3dtexture/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/animimage/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/arc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/bar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/button/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/buttonmatrix/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/calendar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/canvas/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/chart/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/checkbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/dropdown/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/image/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/imagebutton/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/keyboard/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/label/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/led/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/line/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/list/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/lottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/menu/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/msgbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/objx_templ/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/property/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/roller/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/scale/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/slider/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/span/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinner/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/switch/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/table/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tabview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/textarea/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tileview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/win/
+mkdir -p build-VK_RA6M5/lib/lwip/src/apps/mdns/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv4/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv6/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/polarssl/
+mkdir -p build-VK_RA6M5/lib/mbedtls/library/
+mkdir -p build-VK_RA6M5/lib/mbedtls_errors/
+mkdir -p build-VK_RA6M5/lib/oofatfs/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/cdc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/dfu/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/hid/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/midi/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/msc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/usbtmc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/vendor/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/common/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/device/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/portable/renesas/rusb2/
+mkdir -p build-VK_RA6M5/mbedtls/
+mkdir -p build-VK_RA6M5/py/
+mkdir -p build-VK_RA6M5/ra/
+mkdir -p build-VK_RA6M5/shared/libc/
+mkdir -p build-VK_RA6M5/shared/netutils/
+mkdir -p build-VK_RA6M5/shared/readline/
+mkdir -p build-VK_RA6M5/shared/runtime/
+mkdir -p build-VK_RA6M5/shared/timeutils/
+mkdir -p build-VK_RA6M5/shared/tinyusb/
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+MPY asyncio/__init__.py
+MPY asyncio/core.py
+MPY asyncio/event.py
+MPY asyncio/funcs.py
+MPY asyncio/lock.py
+MPY asyncio/stream.py
+MPY uasyncio.py
+MPY dht.py
+MPY onewire.py
+GEN build-VK_RA6M5/frozen_content.c
+CC build-VK_RA6M5/frozen_content.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+CC ../../extmod/machine_bitstream.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modopenamp.c
+CC ../../extmod/modopenamp_remoteproc.c
+CC ../../extmod/modopenamp_remoteproc_store.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/mbedtls/mbedtls_alt.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_group.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_class.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_draw.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_event.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_id_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_pos.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_property.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_tree.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_refr.c
+CC ../../lib/lv_bindings/lvgl/src/display/lv_display.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_3d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_image_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_stm32_hal.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_nema_gfx_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_buf_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_buf_map.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_osa.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/opengles/lv_draw_opengles.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sdl/lv_draw_sdl.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888_premultiplied.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565_swapped.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_letter.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_transform.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_math.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_pending.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/drm/lv_linux_drm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/fb/lv_linux_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ft81x/lv_ft81x.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ili9341/lv_ili9341.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7735/lv_st7735.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7789/lv_st7789.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7796/lv_st7796.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/lv_st_ltdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/evdev/lv_evdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_glfw_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_debug.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_driver.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_texture.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_libinput.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_xkb.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_entry.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_lcd.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_libuv.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_profiler.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/qnx/lv_qnx.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mouse.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mousewheel.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_private.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland_smm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_dmabuf.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer_axis.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_seat.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window_decorations.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_xdg_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_input.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_input.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_binfont_loader.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_fmt_txt.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_10.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_12.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14_aligned.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_18.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_20.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_22.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_24.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_26.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28_compressed.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_30.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_32.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_34.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_36.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_38.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_40.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_42.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_44.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_46.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_48.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_8.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_8.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/flex/lv_flex.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/grid/lv_grid.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/lv_layout.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/code128.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/lv_barcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bin_decoder/lv_bin_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bmp/lv_bmp.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlparse.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlrole.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_impl.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_ns.c
+CC ../../lib/lv_bindings/lvgl/src/libs/ffmpeg/lv_ffmpeg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_glyph.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_image.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_outline.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_ftsystem.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_cbfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_littlefs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_memfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_posix.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_stdio.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_win32.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/gifdec.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/lv_gif.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libpng/lv_libpng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lv_lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lz4/lz4.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/lv_qrcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/qrcodegen.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rle/lv_rle.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rlottie/lv_rlottie.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_parser.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_render.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_token.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/lv_tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/lv_init.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_header_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache_entry.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim_timeline.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_area.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_array.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_async.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_bidi.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_circle_buf.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color_op.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_event.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_fs.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_grad.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_iter.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_log.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_lru.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_math.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_palette.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_profiler_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_templ.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text_ap.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_timer.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_tree.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_utils.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_cmsis_rtos2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_freertos.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_linux.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_mqx.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os_none.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_pthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_sdl2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_windows.c
+CC ../../lib/lv_bindings/lvgl/src/others/file_explorer/lv_file_explorer.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager_recycle.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/gridnav/lv_gridnav.c
+CC ../../lib/lv_bindings/lvgl/src/others/ime/lv_ime_pinyin.c
+CC ../../lib/lv_bindings/lvgl/src/others/imgfont/lv_imgfont.c
+CC ../../lib/lv_bindings/lvgl/src/others/monkey/lv_monkey.c
+CC ../../lib/lv_bindings/lvgl/src/others/observer/lv_observer.c
+CC ../../lib/lv_bindings/lvgl/src/others/snapshot/lv_snapshot.c
+CC ../../lib/lv_bindings/lvgl/src/others/sysmon/lv_sysmon.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_display.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_helpers.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_screenshot_compare.c
+CC ../../lib/lv_bindings/lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_base_types.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_component.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_style.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_update.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_utils.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_widget.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_arc_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_bar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_buttonmatrix_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_button_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_calendar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_canvas_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_chart_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_checkbox_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_dropdown_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_event_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_image_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_keyboard_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_label_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_obj_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_roller_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_scale_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_slider_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_spangroup_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_table_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_tabview_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_textarea_parser.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_mem_core_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_sprintf_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_string_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_tlsf.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_mem_core_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_sprintf_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_string_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/lv_mem.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/micropython/lv_mem_core_micropython.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_string_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/uefi/lv_mem_core_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/themes/default/lv_theme_default.c
+CC ../../lib/lv_bindings/lvgl/src/themes/lv_theme.c
+CC ../../lib/lv_bindings/lvgl/src/themes/mono/lv_theme_mono.c
+CC ../../lib/lv_bindings/lvgl/src/themes/simple/lv_theme_simple.c
+CC ../../lib/lv_bindings/lvgl/src/tick/lv_tick.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/3dtexture/lv_3dtexture.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/animimage/lv_animimage.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/arc/lv_arc.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/bar/lv_bar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/button/lv_button.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_chinese.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/canvas/lv_canvas.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/chart/lv_chart.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/checkbox/lv_checkbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/dropdown/lv_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/image/lv_image.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/imagebutton/lv_imagebutton.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/keyboard/lv_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/label/lv_label.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/led/lv_led.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/line/lv_line.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/list/lv_list.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/lottie/lv_lottie.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/menu/lv_menu.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/msgbox/lv_msgbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/objx_templ/lv_objx_templ.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_animimage_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_dropdown_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_image_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_keyboard_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_label_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_obj_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_roller_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_slider_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_style_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_textarea_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/roller/lv_roller.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/scale/lv_scale.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/slider/lv_slider.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/span/lv_span.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinbox/lv_spinbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinner/lv_spinner.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/switch/lv_switch.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/table/lv_table.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tabview/lv_tabview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/textarea/lv_textarea.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tileview/lv_tileview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/win/lv_win.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../lib/mbedtls_errors/mp_mbedtls_errors.c
+CC ../../lib/mbedtls/library/aes.c
+CC ../../lib/mbedtls/library/aesni.c
+CC ../../lib/mbedtls/library/asn1parse.c
+CC ../../lib/mbedtls/library/asn1write.c
+CC ../../lib/mbedtls/library/base64.c
+CC ../../lib/mbedtls/library/bignum_core.c
+CC ../../lib/mbedtls/library/bignum_mod.c
+CC ../../lib/mbedtls/library/bignum_mod_raw.c
+CC ../../lib/mbedtls/library/bignum.c
+CC ../../lib/mbedtls/library/camellia.c
+CC ../../lib/mbedtls/library/ccm.c
+CC ../../lib/mbedtls/library/chacha20.c
+CC ../../lib/mbedtls/library/chachapoly.c
+CC ../../lib/mbedtls/library/cipher.c
+CC ../../lib/mbedtls/library/cipher_wrap.c
+CC ../../lib/mbedtls/library/nist_kw.c
+CC ../../lib/mbedtls/library/aria.c
+CC ../../lib/mbedtls/library/cmac.c
+CC ../../lib/mbedtls/library/constant_time.c
+CC ../../lib/mbedtls/library/mps_reader.c
+CC ../../lib/mbedtls/library/mps_trace.c
+CC ../../lib/mbedtls/library/ctr_drbg.c
+CC ../../lib/mbedtls/library/debug.c
+CC ../../lib/mbedtls/library/des.c
+CC ../../lib/mbedtls/library/dhm.c
+CC ../../lib/mbedtls/library/ecdh.c
+CC ../../lib/mbedtls/library/ecdsa.c
+CC ../../lib/mbedtls/library/ecjpake.c
+CC ../../lib/mbedtls/library/ecp.c
+CC ../../lib/mbedtls/library/ecp_curves.c
+CC ../../lib/mbedtls/library/entropy.c
+CC ../../lib/mbedtls/library/entropy_poll.c
+CC ../../lib/mbedtls/library/gcm.c
+CC ../../lib/mbedtls/library/hmac_drbg.c
+CC ../../lib/mbedtls/library/md5.c
+CC ../../lib/mbedtls/library/md.c
+CC ../../lib/mbedtls/library/oid.c
+CC ../../lib/mbedtls/library/padlock.c
+CC ../../lib/mbedtls/library/pem.c
+CC ../../lib/mbedtls/library/pk.c
+CC ../../lib/mbedtls/library/pkcs12.c
+CC ../../lib/mbedtls/library/pkcs5.c
+CC ../../lib/mbedtls/library/pkparse.c
+CC ../../lib/mbedtls/library/pk_wrap.c
+CC ../../lib/mbedtls/library/pkwrite.c
+CC ../../lib/mbedtls/library/platform.c
+CC ../../lib/mbedtls/library/platform_util.c
+CC ../../lib/mbedtls/library/poly1305.c
+CC ../../lib/mbedtls/library/ripemd160.c
+CC ../../lib/mbedtls/library/rsa.c
+CC ../../lib/mbedtls/library/rsa_alt_helpers.c
+CC ../../lib/mbedtls/library/sha1.c
+CC ../../lib/mbedtls/library/sha256.c
+CC ../../lib/mbedtls/library/sha512.c
+CC ../../lib/mbedtls/library/ssl_cache.c
+CC ../../lib/mbedtls/library/ssl_ciphersuites.c
+CC ../../lib/mbedtls/library/ssl_client.c
+CC ../../lib/mbedtls/library/ssl_cookie.c
+CC ../../lib/mbedtls/library/ssl_debug_helpers_generated.c
+CC ../../lib/mbedtls/library/ssl_msg.c
+CC ../../lib/mbedtls/library/ssl_ticket.c
+CC ../../lib/mbedtls/library/ssl_tls.c
+CC ../../lib/mbedtls/library/ssl_tls12_client.c
+CC ../../lib/mbedtls/library/ssl_tls12_server.c
+CC ../../lib/mbedtls/library/timing.c
+CC ../../lib/mbedtls/library/x509.c
+CC ../../lib/mbedtls/library/x509_create.c
+CC ../../lib/mbedtls/library/x509_crl.c
+CC ../../lib/mbedtls/library/x509_crt.c
+CC ../../lib/mbedtls/library/x509_csr.c
+CC ../../lib/mbedtls/library/x509write_crt.c
+CC ../../lib/mbedtls/library/x509write_csr.c
+CC ../../shared/netutils/netutils.c
+CC ../../lib/lwip/src/apps/mdns/mdns.c
+CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
+CC ../../lib/lwip/src/apps/mdns/mdns_out.c
+CC ../../lib/lwip/src/core/def.c
+CC ../../lib/lwip/src/core/dns.c
+CC ../../lib/lwip/src/core/inet_chksum.c
+CC ../../lib/lwip/src/core/init.c
+CC ../../lib/lwip/src/core/ip.c
+CC ../../lib/lwip/src/core/mem.c
+CC ../../lib/lwip/src/core/memp.c
+CC ../../lib/lwip/src/core/netif.c
+CC ../../lib/lwip/src/core/pbuf.c
+CC ../../lib/lwip/src/core/raw.c
+CC ../../lib/lwip/src/core/stats.c
+CC ../../lib/lwip/src/core/sys.c
+CC ../../lib/lwip/src/core/tcp.c
+CC ../../lib/lwip/src/core/tcp_in.c
+CC ../../lib/lwip/src/core/tcp_out.c
+CC ../../lib/lwip/src/core/timeouts.c
+CC ../../lib/lwip/src/core/udp.c
+CC ../../lib/lwip/src/core/ipv4/acd.c
+CC ../../lib/lwip/src/core/ipv4/autoip.c
+CC ../../lib/lwip/src/core/ipv4/dhcp.c
+CC ../../lib/lwip/src/core/ipv4/etharp.c
+CC ../../lib/lwip/src/core/ipv4/icmp.c
+CC ../../lib/lwip/src/core/ipv4/igmp.c
+CC ../../lib/lwip/src/core/ipv4/ip4_addr.c
+CC ../../lib/lwip/src/core/ipv4/ip4.c
+CC ../../lib/lwip/src/core/ipv4/ip4_frag.c
+CC ../../lib/lwip/src/core/ipv6/dhcp6.c
+CC ../../lib/lwip/src/core/ipv6/ethip6.c
+CC ../../lib/lwip/src/core/ipv6/icmp6.c
+CC ../../lib/lwip/src/core/ipv6/inet6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_addr.c
+CC ../../lib/lwip/src/core/ipv6/ip6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_frag.c
+CC ../../lib/lwip/src/core/ipv6/mld6.c
+CC ../../lib/lwip/src/core/ipv6/nd6.c
+CC ../../lib/lwip/src/netif/ethernet.c
+CC ../../lib/lwip/src/netif/ppp/auth.c
+CC ../../lib/lwip/src/netif/ppp/ccp.c
+CC ../../lib/lwip/src/netif/ppp/chap-md5.c
+CC ../../lib/lwip/src/netif/ppp/chap_ms.c
+CC ../../lib/lwip/src/netif/ppp/chap-new.c
+CC ../../lib/lwip/src/netif/ppp/demand.c
+CC ../../lib/lwip/src/netif/ppp/eap.c
+CC ../../lib/lwip/src/netif/ppp/ecp.c
+CC ../../lib/lwip/src/netif/ppp/eui64.c
+CC ../../lib/lwip/src/netif/ppp/fsm.c
+CC ../../lib/lwip/src/netif/ppp/ipcp.c
+CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
+CC ../../lib/lwip/src/netif/ppp/lcp.c
+CC ../../lib/lwip/src/netif/ppp/magic.c
+CC ../../lib/lwip/src/netif/ppp/mppe.c
+CC ../../lib/lwip/src/netif/ppp/multilink.c
+CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
+CC ../../lib/lwip/src/netif/ppp/pppapi.c
+CC ../../lib/lwip/src/netif/ppp/ppp.c
+CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
+CC ../../lib/lwip/src/netif/ppp/pppoe.c
+CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
+CC ../../lib/lwip/src/netif/ppp/pppos.c
+CC ../../lib/lwip/src/netif/ppp/upap.c
+CC ../../lib/lwip/src/netif/ppp/utils.c
+CC ../../lib/lwip/src/netif/ppp/vj.c
+CC mbedtls/mbedtls_port.c
+CC ../../lib/libm/math.c
+CC ../../lib/libm/acoshf.c
+CC ../../lib/libm/asinfacosf.c
+CC ../../lib/libm/asinhf.c
+CC ../../lib/libm/atan2f.c
+CC ../../lib/libm/atanf.c
+CC ../../lib/libm/atanhf.c
+CC ../../lib/libm/ef_rem_pio2.c
+CC ../../lib/libm/erf_lgamma.c
+CC ../../lib/libm/fmodf.c
+CC ../../lib/libm/kf_cos.c
+CC ../../lib/libm/kf_rem_pio2.c
+CC ../../lib/libm/kf_sin.c
+CC ../../lib/libm/kf_tan.c
+CC ../../lib/libm/log1pf.c
+CC ../../lib/libm/nearbyintf.c
+CC ../../lib/libm/roundf.c
+CC ../../lib/libm/sf_cos.c
+CC ../../lib/libm/sf_erf.c
+CC ../../lib/libm/sf_frexp.c
+CC ../../lib/libm/sf_ldexp.c
+CC ../../lib/libm/sf_modf.c
+CC ../../lib/libm/sf_sin.c
+CC ../../lib/libm/sf_tan.c
+CC ../../lib/libm/wf_lgamma.c
+CC ../../lib/libm/wf_tgamma.c
+CC ../../lib/libm/thumb_vfp_sqrtf.c
+CC ../../shared/libc/string0.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_clocks.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_common.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_delay.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_guard.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_io.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_security.c
+CC ../../lib/fsp/ra/fsp/src/r_ioport/r_ioport.c
+CC ../../lib/fsp/ra/fsp/src/r_sci_uart/r_sci_uart.c
+CC ../../lib/fsp/ra/fsp/src/r_ospi/r_ospi.c
+CC ../../lib/fsp/ra/fsp/src/r_qspi/r_qspi.c
+CC ../../lib/fsp/ra/fsp/src/r_sdhi/r_sdhi.c
+CC ../../lib/fsp/ra/fsp/src/r_dtc/r_dtc.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/r_ether_phy_target_ics1894.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/r_ether_phy.c
+CC ../../lib/fsp/ra/fsp/src/r_ether/r_ether.c
+CC ../../lib/fsp/ra/fsp/src/r_lpm/r_lpm.c
+CC ../../lib/fsp/ra/fsp/src/r_flash_hp/r_flash_hp.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_ecc.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_sha.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_aes.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/r_sce_private.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p00.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p20.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p26.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p81.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p82.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p92.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p40.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func050.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func051.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func052.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func053.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func054.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func100.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func101.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func040.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func048.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func102.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func103.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_subprc01.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_hal.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/board_init.c
+CC boards/VK_RA6M5/ra_gen/common_data.c
+CC boards/VK_RA6M5/ra_gen/hal_data.c
+CC boards/VK_RA6M5/ra_gen/pin_data.c
+CC boards/VK_RA6M5/ra_gen/vector_data.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c
+AS ../../shared/runtime/gchelper_thumb2.s
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506221955.log b/ports/renesas-ra/VK_RA6M5_build_202506221955.log
new file mode 100644
index 00000000..daeabfa3
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506221955.log
@@ -0,0 +1,1131 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/
+mkdir -p build-VK_RA6M5/boards/VK_RA6M5/ra_gen/
+mkdir -p build-VK_RA6M5/build-VK_RA6M5/lvgl/
+mkdir -p build-VK_RA6M5/drivers/bus/
+mkdir -p build-VK_RA6M5/drivers/dht/
+mkdir -p build-VK_RA6M5/drivers/memory/
+mkdir -p build-VK_RA6M5/extmod/
+mkdir -p build-VK_RA6M5/extmod/mbedtls/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/bsp/mcu/all/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_dtc/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_flash_hp/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ioport/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_lpm/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_ospi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_qspi/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sci_uart/
+mkdir -p build-VK_RA6M5/lib/fsp/ra/fsp/src/r_sdhi/
+mkdir -p build-VK_RA6M5/lib/libm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/core/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/display/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/dma2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nema_gfx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/g2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/pxp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/nxp/vglite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/opengles/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/renesas/dave2d/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/sw/blend/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/draw/vg_lite/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/drm/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/fb/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ft81x/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/ili9341/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/lcd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7735/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7789/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st7796/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/evdev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/glfw/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/libinput/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/nuttx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/qnx/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/sdl/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/wayland/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/windows/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/drivers/x11/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/font/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/indev/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/flex/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/layouts/grid/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/barcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bin_decoder/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/bmp/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/expat/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/ffmpeg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/freetype/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/fsdrv/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/gif/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/libpng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lodepng/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/lz4/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/qrcode/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rle/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/rlottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/svg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tiny_ttf/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/libs/tjpgd/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/class/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/misc/cache/instance/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/osal/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/file_explorer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/font_manager/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/fragment/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/gridnav/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/ime/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/imgfont/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/monkey/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/observer/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/snapshot/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/sysmon/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/test/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/vg_lite_tvg/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/others/xml/parsers/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/builtin/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/clib/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/micropython/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/rtthread/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/stdlib/uefi/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/default/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/mono/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/themes/simple/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/tick/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/3dtexture/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/animimage/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/arc/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/bar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/button/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/buttonmatrix/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/calendar/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/canvas/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/chart/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/checkbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/dropdown/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/image/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/imagebutton/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/keyboard/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/label/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/led/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/line/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/list/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/lottie/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/menu/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/msgbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/objx_templ/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/property/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/roller/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/scale/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/slider/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/span/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinbox/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/spinner/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/switch/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/table/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tabview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/textarea/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/tileview/
+mkdir -p build-VK_RA6M5/lib/lv_bindings/lvgl/src/widgets/win/
+mkdir -p build-VK_RA6M5/lib/lwip/src/apps/mdns/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv4/
+mkdir -p build-VK_RA6M5/lib/lwip/src/core/ipv6/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/
+mkdir -p build-VK_RA6M5/lib/lwip/src/netif/ppp/polarssl/
+mkdir -p build-VK_RA6M5/lib/mbedtls/library/
+mkdir -p build-VK_RA6M5/lib/mbedtls_errors/
+mkdir -p build-VK_RA6M5/lib/oofatfs/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/cdc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/dfu/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/hid/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/midi/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/msc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/usbtmc/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/class/vendor/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/common/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/device/
+mkdir -p build-VK_RA6M5/lib/tinyusb/src/portable/renesas/rusb2/
+mkdir -p build-VK_RA6M5/mbedtls/
+mkdir -p build-VK_RA6M5/py/
+mkdir -p build-VK_RA6M5/ra/
+mkdir -p build-VK_RA6M5/shared/libc/
+mkdir -p build-VK_RA6M5/shared/netutils/
+mkdir -p build-VK_RA6M5/shared/readline/
+mkdir -p build-VK_RA6M5/shared/runtime/
+mkdir -p build-VK_RA6M5/shared/timeutils/
+mkdir -p build-VK_RA6M5/shared/tinyusb/
+GEN build-VK_RA6M5/genhdr/mpversion.h
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/compressed.collected
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+Compressed data updated
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.data.h
+QSTR updated
+Module registrations updated
+Root pointer registrations updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+GEN build-VK_RA6M5/genhdr/moduledefs.h
+GEN build-VK_RA6M5/genhdr/root_pointers.h
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+CC ../../extmod/machine_bitstream.c
+MPY asyncio/__init__.py
+MPY asyncio/core.py
+MPY asyncio/event.py
+MPY asyncio/funcs.py
+MPY asyncio/lock.py
+MPY asyncio/stream.py
+MPY uasyncio.py
+MPY dht.py
+MPY onewire.py
+GEN build-VK_RA6M5/frozen_content.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modopenamp.c
+CC ../../extmod/modopenamp_remoteproc.c
+CC ../../extmod/modopenamp_remoteproc_store.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/mbedtls/mbedtls_alt.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_group.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_class.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_draw.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_event.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_id_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_pos.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_property.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_obj_tree.c
+CC ../../lib/lv_bindings/lvgl/src/core/lv_refr.c
+CC ../../lib/lv_bindings/lvgl/src/display/lv_display.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/dma2d/lv_draw_dma2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_3d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_draw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/lv_image_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_stm32_hal.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_draw_nema_gfx_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nema_gfx/lv_nema_gfx_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_buf_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_draw_g2d_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_buf_map.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/g2d/lv_g2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_buf_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_draw_pxp_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_cfg.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_osa.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/pxp/lv_pxp_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_buf_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_draw_vglite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/opengles/lv_draw_opengles.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_image.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_mask_rectangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/renesas/dave2d/lv_draw_dave2d_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sdl/lv_draw_sdl.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_al88.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_argb8888_premultiplied.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_i1.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_l8.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb565_swapped.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/blend/lv_draw_sw_blend_to_rgb888.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_letter.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_transform.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_utils.c
+CC ../../lib/lv_bindings/lvgl/src/draw/sw/lv_draw_sw_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_buf_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_arc.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_border.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_box_shadow.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_fill.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_img.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_label.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_layer.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_line.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_mask_rect.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_triangle.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_draw_vg_lite_vector.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_grad.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_math.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_path.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_pending.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_stroke.c
+CC ../../lib/lv_bindings/lvgl/src/draw/vg_lite/lv_vg_lite_utils.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/drm/lv_linux_drm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/fb/lv_linux_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ft81x/lv_ft81x.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/ili9341/lv_ili9341.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/lcd/lv_lcd_generic_mipi.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/renesas_glcdc/lv_renesas_glcdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7735/lv_st7735.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7789/lv_st7789.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st7796/lv_st7796.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/display/st_ltdc/lv_st_ltdc.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/evdev/lv_evdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_glfw_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_debug.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_driver.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/glfw/lv_opengles_texture.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_libinput.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/libinput/lv_xkb.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_entry.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_fbdev.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_lcd.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_libuv.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_profiler.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/qnx/lv_qnx.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mouse.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_mousewheel.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/sdl/lv_sdl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_indev_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/uefi/lv_uefi_private.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wayland_smm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_cache.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_dmabuf.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_pointer_axis.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_seat.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_shm.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_touch.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_window_decorations.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/wayland/lv_wl_xdg_shell.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_context.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/windows/lv_windows_input.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_display.c
+CC ../../lib/lv_bindings/lvgl/src/drivers/x11/lv_x11_input.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_binfont_loader.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_fmt_txt.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_10.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_12.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_14_aligned.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_18.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_20.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_22.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_24.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_26.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_28_compressed.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_30.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_32.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_34.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_36.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_38.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_40.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_42.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_44.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_46.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_48.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_montserrat_8.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_simsun_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_14_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_source_han_sans_sc_16_cjk.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_16.c
+CC ../../lib/lv_bindings/lvgl/src/font/lv_font_unscii_8.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/indev/lv_indev_scroll.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/flex/lv_flex.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/grid/lv_grid.c
+CC ../../lib/lv_bindings/lvgl/src/layouts/lv_layout.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/code128.c
+CC ../../lib/lv_bindings/lvgl/src/libs/barcode/lv_barcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bin_decoder/lv_bin_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/bmp/lv_bmp.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlparse.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmlrole.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_impl.c
+CC ../../lib/lv_bindings/lvgl/src/libs/expat/xmltok_ns.c
+CC ../../lib/lv_bindings/lvgl/src/libs/ffmpeg/lv_ffmpeg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_glyph.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_image.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_freetype_outline.c
+CC ../../lib/lv_bindings/lvgl/src/libs/freetype/lv_ftsystem.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_cbfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_fatfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_littlefs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_memfs.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_posix.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_stdio.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/libs/fsdrv/lv_fs_win32.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/gifdec.c
+CC ../../lib/lv_bindings/lvgl/src/libs/gif/lv_gif.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.c
+CC ../../lib/lv_bindings/lvgl/src/libs/libpng/lv_libpng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lodepng/lv_lodepng.c
+CC ../../lib/lv_bindings/lvgl/src/libs/lz4/lz4.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/lv_qrcode.c
+CC ../../lib/lv_bindings/lvgl/src/libs/qrcode/qrcodegen.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rle/lv_rle.c
+CC ../../lib/lv_bindings/lvgl/src/libs/rlottie/lv_rlottie.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_decoder.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_parser.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_render.c
+CC ../../lib/lv_bindings/lvgl/src/libs/svg/lv_svg_token.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tiny_ttf/lv_tiny_ttf.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/lv_tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/libs/tjpgd/tjpgd.c
+CC ../../lib/lv_bindings/lvgl/src/lv_init.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/class/lv_cache_lru_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/instance/lv_image_header_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache.c
+CC ../../lib/lv_bindings/lvgl/src/misc/cache/lv_cache_entry.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_anim_timeline.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_area.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_array.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_async.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_bidi.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_circle_buf.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_color_op.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_event.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_fs.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_grad.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_iter.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_ll.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_log.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_lru.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_math.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_palette.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_profiler_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_rb.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_style_gen.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_templ.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_text_ap.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_timer.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_tree.c
+CC ../../lib/lv_bindings/lvgl/src/misc/lv_utils.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_cmsis_rtos2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_freertos.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_linux.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_mqx.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_os_none.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_pthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_sdl2.c
+CC ../../lib/lv_bindings/lvgl/src/osal/lv_windows.c
+CC ../../lib/lv_bindings/lvgl/src/others/file_explorer/lv_file_explorer.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/font_manager/lv_font_manager_recycle.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment.c
+CC ../../lib/lv_bindings/lvgl/src/others/fragment/lv_fragment_manager.c
+CC ../../lib/lv_bindings/lvgl/src/others/gridnav/lv_gridnav.c
+CC ../../lib/lv_bindings/lvgl/src/others/ime/lv_ime_pinyin.c
+CC ../../lib/lv_bindings/lvgl/src/others/imgfont/lv_imgfont.c
+CC ../../lib/lv_bindings/lvgl/src/others/monkey/lv_monkey.c
+CC ../../lib/lv_bindings/lvgl/src/others/observer/lv_observer.c
+CC ../../lib/lv_bindings/lvgl/src/others/snapshot/lv_snapshot.c
+CC ../../lib/lv_bindings/lvgl/src/others/sysmon/lv_sysmon.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_display.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_helpers.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_indev_gesture.c
+CC ../../lib/lv_bindings/lvgl/src/others/test/lv_test_screenshot_compare.c
+CC ../../lib/lv_bindings/lvgl/src/others/vg_lite_tvg/vg_lite_matrix.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_base_types.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_component.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_style.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_update.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_utils.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/lv_xml_widget.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_arc_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_bar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_buttonmatrix_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_button_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_calendar_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_canvas_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_chart_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_checkbox_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_dropdown_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_event_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_image_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_keyboard_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_label_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_obj_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_roller_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_scale_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_slider_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_spangroup_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_table_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_tabview_parser.c
+CC ../../lib/lv_bindings/lvgl/src/others/xml/parsers/lv_xml_textarea_parser.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_mem_core_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_sprintf_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_string_builtin.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/builtin/lv_tlsf.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_mem_core_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_sprintf_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/clib/lv_string_clib.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/lv_mem.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/micropython/lv_mem_core_micropython.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_mem_core_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_sprintf_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/rtthread/lv_string_rtthread.c
+CC ../../lib/lv_bindings/lvgl/src/stdlib/uefi/lv_mem_core_uefi.c
+CC ../../lib/lv_bindings/lvgl/src/themes/default/lv_theme_default.c
+CC ../../lib/lv_bindings/lvgl/src/themes/lv_theme.c
+CC ../../lib/lv_bindings/lvgl/src/themes/mono/lv_theme_mono.c
+CC ../../lib/lv_bindings/lvgl/src/themes/simple/lv_theme_simple.c
+CC ../../lib/lv_bindings/lvgl/src/tick/lv_tick.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/3dtexture/lv_3dtexture.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/animimage/lv_animimage.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/arc/lv_arc.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/bar/lv_bar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/button/lv_button.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_chinese.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_arrow.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/calendar/lv_calendar_header_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/canvas/lv_canvas.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/chart/lv_chart.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/checkbox/lv_checkbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/dropdown/lv_dropdown.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/image/lv_image.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/imagebutton/lv_imagebutton.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/keyboard/lv_keyboard.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/label/lv_label.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/led/lv_led.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/line/lv_line.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/list/lv_list.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/lottie/lv_lottie.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/menu/lv_menu.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/msgbox/lv_msgbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/objx_templ/lv_objx_templ.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_animimage_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_dropdown_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_image_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_keyboard_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_label_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_obj_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_roller_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_slider_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_style_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/property/lv_textarea_properties.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/roller/lv_roller.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/scale/lv_scale.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/slider/lv_slider.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/span/lv_span.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinbox/lv_spinbox.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/spinner/lv_spinner.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/switch/lv_switch.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/table/lv_table.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tabview/lv_tabview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/textarea/lv_textarea.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/tileview/lv_tileview.c
+CC ../../lib/lv_bindings/lvgl/src/widgets/win/lv_win.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../lib/mbedtls_errors/mp_mbedtls_errors.c
+CC ../../lib/mbedtls/library/aes.c
+CC ../../lib/mbedtls/library/aesni.c
+CC ../../lib/mbedtls/library/asn1parse.c
+CC ../../lib/mbedtls/library/asn1write.c
+CC ../../lib/mbedtls/library/base64.c
+CC ../../lib/mbedtls/library/bignum_core.c
+CC ../../lib/mbedtls/library/bignum_mod.c
+CC ../../lib/mbedtls/library/bignum_mod_raw.c
+CC ../../lib/mbedtls/library/bignum.c
+CC ../../lib/mbedtls/library/camellia.c
+CC ../../lib/mbedtls/library/ccm.c
+CC ../../lib/mbedtls/library/chacha20.c
+CC ../../lib/mbedtls/library/chachapoly.c
+CC ../../lib/mbedtls/library/cipher.c
+CC ../../lib/mbedtls/library/cipher_wrap.c
+CC ../../lib/mbedtls/library/nist_kw.c
+CC ../../lib/mbedtls/library/aria.c
+CC ../../lib/mbedtls/library/cmac.c
+CC ../../lib/mbedtls/library/constant_time.c
+CC ../../lib/mbedtls/library/mps_reader.c
+CC ../../lib/mbedtls/library/mps_trace.c
+CC ../../lib/mbedtls/library/ctr_drbg.c
+CC ../../lib/mbedtls/library/debug.c
+CC ../../lib/mbedtls/library/des.c
+CC ../../lib/mbedtls/library/dhm.c
+CC ../../lib/mbedtls/library/ecdh.c
+CC ../../lib/mbedtls/library/ecdsa.c
+CC ../../lib/mbedtls/library/ecjpake.c
+CC ../../lib/mbedtls/library/ecp.c
+CC ../../lib/mbedtls/library/ecp_curves.c
+CC ../../lib/mbedtls/library/entropy.c
+CC ../../lib/mbedtls/library/entropy_poll.c
+CC ../../lib/mbedtls/library/gcm.c
+CC ../../lib/mbedtls/library/hmac_drbg.c
+CC ../../lib/mbedtls/library/md5.c
+CC ../../lib/mbedtls/library/md.c
+CC ../../lib/mbedtls/library/oid.c
+CC ../../lib/mbedtls/library/padlock.c
+CC ../../lib/mbedtls/library/pem.c
+CC ../../lib/mbedtls/library/pk.c
+CC ../../lib/mbedtls/library/pkcs12.c
+CC ../../lib/mbedtls/library/pkcs5.c
+CC ../../lib/mbedtls/library/pkparse.c
+CC ../../lib/mbedtls/library/pk_wrap.c
+CC ../../lib/mbedtls/library/pkwrite.c
+CC ../../lib/mbedtls/library/platform.c
+CC ../../lib/mbedtls/library/platform_util.c
+CC ../../lib/mbedtls/library/poly1305.c
+CC ../../lib/mbedtls/library/ripemd160.c
+CC ../../lib/mbedtls/library/rsa.c
+CC ../../lib/mbedtls/library/rsa_alt_helpers.c
+CC ../../lib/mbedtls/library/sha1.c
+CC ../../lib/mbedtls/library/sha256.c
+CC ../../lib/mbedtls/library/sha512.c
+CC ../../lib/mbedtls/library/ssl_cache.c
+CC ../../lib/mbedtls/library/ssl_ciphersuites.c
+CC ../../lib/mbedtls/library/ssl_client.c
+CC ../../lib/mbedtls/library/ssl_cookie.c
+CC ../../lib/mbedtls/library/ssl_debug_helpers_generated.c
+CC ../../lib/mbedtls/library/ssl_msg.c
+CC ../../lib/mbedtls/library/ssl_ticket.c
+CC ../../lib/mbedtls/library/ssl_tls.c
+CC ../../lib/mbedtls/library/ssl_tls12_client.c
+CC ../../lib/mbedtls/library/ssl_tls12_server.c
+CC ../../lib/mbedtls/library/timing.c
+CC ../../lib/mbedtls/library/x509.c
+CC ../../lib/mbedtls/library/x509_create.c
+CC ../../lib/mbedtls/library/x509_crl.c
+CC ../../lib/mbedtls/library/x509_crt.c
+CC ../../lib/mbedtls/library/x509_csr.c
+CC ../../lib/mbedtls/library/x509write_crt.c
+CC ../../lib/mbedtls/library/x509write_csr.c
+CC ../../shared/netutils/netutils.c
+CC ../../lib/lwip/src/apps/mdns/mdns.c
+CC ../../lib/lwip/src/apps/mdns/mdns_domain.c
+CC ../../lib/lwip/src/apps/mdns/mdns_out.c
+CC ../../lib/lwip/src/core/def.c
+CC ../../lib/lwip/src/core/dns.c
+CC ../../lib/lwip/src/core/inet_chksum.c
+CC ../../lib/lwip/src/core/init.c
+CC ../../lib/lwip/src/core/ip.c
+CC ../../lib/lwip/src/core/mem.c
+CC ../../lib/lwip/src/core/memp.c
+CC ../../lib/lwip/src/core/netif.c
+CC ../../lib/lwip/src/core/pbuf.c
+CC ../../lib/lwip/src/core/raw.c
+CC ../../lib/lwip/src/core/stats.c
+CC ../../lib/lwip/src/core/sys.c
+CC ../../lib/lwip/src/core/tcp.c
+CC ../../lib/lwip/src/core/tcp_in.c
+CC ../../lib/lwip/src/core/tcp_out.c
+CC ../../lib/lwip/src/core/timeouts.c
+CC ../../lib/lwip/src/core/udp.c
+CC ../../lib/lwip/src/core/ipv4/acd.c
+CC ../../lib/lwip/src/core/ipv4/autoip.c
+CC ../../lib/lwip/src/core/ipv4/dhcp.c
+CC ../../lib/lwip/src/core/ipv4/etharp.c
+CC ../../lib/lwip/src/core/ipv4/icmp.c
+CC ../../lib/lwip/src/core/ipv4/igmp.c
+CC ../../lib/lwip/src/core/ipv4/ip4_addr.c
+CC ../../lib/lwip/src/core/ipv4/ip4.c
+CC ../../lib/lwip/src/core/ipv4/ip4_frag.c
+CC ../../lib/lwip/src/core/ipv6/dhcp6.c
+CC ../../lib/lwip/src/core/ipv6/ethip6.c
+CC ../../lib/lwip/src/core/ipv6/icmp6.c
+CC ../../lib/lwip/src/core/ipv6/inet6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_addr.c
+CC ../../lib/lwip/src/core/ipv6/ip6.c
+CC ../../lib/lwip/src/core/ipv6/ip6_frag.c
+CC ../../lib/lwip/src/core/ipv6/mld6.c
+CC ../../lib/lwip/src/core/ipv6/nd6.c
+CC ../../lib/lwip/src/netif/ethernet.c
+CC ../../lib/lwip/src/netif/ppp/auth.c
+CC ../../lib/lwip/src/netif/ppp/ccp.c
+CC ../../lib/lwip/src/netif/ppp/chap-md5.c
+CC ../../lib/lwip/src/netif/ppp/chap_ms.c
+CC ../../lib/lwip/src/netif/ppp/chap-new.c
+CC ../../lib/lwip/src/netif/ppp/demand.c
+CC ../../lib/lwip/src/netif/ppp/eap.c
+CC ../../lib/lwip/src/netif/ppp/ecp.c
+CC ../../lib/lwip/src/netif/ppp/eui64.c
+CC ../../lib/lwip/src/netif/ppp/fsm.c
+CC ../../lib/lwip/src/netif/ppp/ipcp.c
+CC ../../lib/lwip/src/netif/ppp/ipv6cp.c
+CC ../../lib/lwip/src/netif/ppp/lcp.c
+CC ../../lib/lwip/src/netif/ppp/magic.c
+CC ../../lib/lwip/src/netif/ppp/mppe.c
+CC ../../lib/lwip/src/netif/ppp/multilink.c
+CC ../../lib/lwip/src/netif/ppp/polarssl/md5.c
+CC ../../lib/lwip/src/netif/ppp/pppapi.c
+CC ../../lib/lwip/src/netif/ppp/ppp.c
+CC ../../lib/lwip/src/netif/ppp/pppcrypt.c
+CC ../../lib/lwip/src/netif/ppp/pppoe.c
+CC ../../lib/lwip/src/netif/ppp/pppol2tp.c
+CC ../../lib/lwip/src/netif/ppp/pppos.c
+CC ../../lib/lwip/src/netif/ppp/upap.c
+CC ../../lib/lwip/src/netif/ppp/utils.c
+CC ../../lib/lwip/src/netif/ppp/vj.c
+CC mbedtls/mbedtls_port.c
+CC ../../lib/libm/math.c
+CC ../../lib/libm/acoshf.c
+CC ../../lib/libm/asinfacosf.c
+CC ../../lib/libm/asinhf.c
+CC ../../lib/libm/atan2f.c
+CC ../../lib/libm/atanf.c
+CC ../../lib/libm/atanhf.c
+CC ../../lib/libm/ef_rem_pio2.c
+CC ../../lib/libm/erf_lgamma.c
+CC ../../lib/libm/fmodf.c
+CC ../../lib/libm/kf_cos.c
+CC ../../lib/libm/kf_rem_pio2.c
+CC ../../lib/libm/kf_sin.c
+CC ../../lib/libm/kf_tan.c
+CC ../../lib/libm/log1pf.c
+CC ../../lib/libm/nearbyintf.c
+CC ../../lib/libm/roundf.c
+CC ../../lib/libm/sf_cos.c
+CC ../../lib/libm/sf_erf.c
+CC ../../lib/libm/sf_frexp.c
+CC ../../lib/libm/sf_ldexp.c
+CC ../../lib/libm/sf_modf.c
+CC ../../lib/libm/sf_sin.c
+CC ../../lib/libm/sf_tan.c
+CC ../../lib/libm/wf_lgamma.c
+CC ../../lib/libm/wf_tgamma.c
+CC ../../lib/libm/thumb_vfp_sqrtf.c
+CC ../../shared/libc/string0.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_clocks.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_common.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_delay.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_guard.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_io.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_irq.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c
+CC ../../lib/fsp/ra/fsp/src/bsp/mcu/all/bsp_security.c
+CC ../../lib/fsp/ra/fsp/src/r_ioport/r_ioport.c
+CC ../../lib/fsp/ra/fsp/src/r_sci_uart/r_sci_uart.c
+CC ../../lib/fsp/ra/fsp/src/r_ospi/r_ospi.c
+CC ../../lib/fsp/ra/fsp/src/r_qspi/r_qspi.c
+CC ../../lib/fsp/ra/fsp/src/r_sdhi/r_sdhi.c
+CC ../../lib/fsp/ra/fsp/src/r_dtc/r_dtc.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/targets/ICS1894/r_ether_phy_target_ics1894.c
+CC ../../lib/fsp/ra/fsp/src/r_ether_phy/r_ether_phy.c
+CC ../../lib/fsp/ra/fsp/src/r_ether/r_ether.c
+CC ../../lib/fsp/ra/fsp/src/r_lpm/r_lpm.c
+CC ../../lib/fsp/ra/fsp/src/r_flash_hp/r_flash_hp.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_ecc.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_sha.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//public/r_sce_aes.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//private/r_sce_private.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p00.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p20.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p26.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p81.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p82.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p92.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_p40.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func050.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func051.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func052.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func053.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func054.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func100.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func101.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func040.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func048.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func102.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_func103.c
+CC ../../lib/fsp/ra/fsp/src/r_sce_protected/crypto_procedures_protected/src/sce9//primitive/r_sce_subprc01.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_hal.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/board_init.c
+CC boards/VK_RA6M5/ra_gen/common_data.c
+CC boards/VK_RA6M5/ra_gen/hal_data.c
+CC boards/VK_RA6M5/ra_gen/pin_data.c
+CC boards/VK_RA6M5/ra_gen/vector_data.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c
+CC ../../lib/fsp/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c
+AS ../../shared/runtime/gchelper_thumb2.s
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+CC build-VK_RA6M5/frozen_content.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222015.log b/ports/renesas-ra/VK_RA6M5_build_202506222015.log
new file mode 100644
index 00000000..07839eb5
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222015.log
@@ -0,0 +1,296 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
+CC ../../py/mpstate.c
+CC ../../py/nlr.c
+CC ../../py/nlrx86.c
+CC ../../py/nlrx64.c
+CC ../../py/nlrthumb.c
+CC ../../py/nlraarch64.c
+CC ../../py/nlrmips.c
+CC ../../py/nlrpowerpc.c
+CC ../../py/nlrxtensa.c
+CC ../../py/nlrrv32.c
+CC ../../py/nlrrv64.c
+CC ../../py/nlrsetjmp.c
+CC ../../py/malloc.c
+CC ../../py/gc_ospi.c
+CC ../../py/modgc_ospi.c
+CC ../../py/gc.c
+CC ../../py/pystack.c
+CC ../../py/qstr.c
+CC ../../py/vstr.c
+CC ../../py/mpprint.c
+CC ../../py/unicode.c
+CC ../../py/mpz.c
+CC ../../py/reader.c
+CC ../../py/lexer.c
+CC ../../py/parse.c
+CC ../../py/scope.c
+CC ../../py/compile.c
+CC ../../py/emitcommon.c
+CC ../../py/emitbc.c
+CC ../../py/asmbase.c
+CC ../../py/asmx64.c
+CC ../../py/emitnx64.c
+CC ../../py/asmx86.c
+CC ../../py/emitnx86.c
+CC ../../py/asmthumb.c
+CC ../../py/emitnthumb.c
+CC ../../py/emitinlinethumb.c
+CC ../../py/asmarm.c
+CC ../../py/emitnarm.c
+CC ../../py/asmxtensa.c
+CC ../../py/emitnxtensa.c
+CC ../../py/emitinlinextensa.c
+CC ../../py/emitnxtensawin.c
+CC ../../py/asmrv32.c
+CC ../../py/emitnrv32.c
+CC ../../py/emitndebug.c
+CC ../../py/formatfloat.c
+CC ../../py/parsenumbase.c
+CC ../../py/parsenum.c
+CC ../../py/emitglue.c
+CC ../../py/persistentcode.c
+CC ../../py/runtime.c
+CC ../../py/runtime_utils.c
+CC ../../py/scheduler.c
+CC ../../py/nativeglue.c
+CC ../../py/pairheap.c
+CC ../../py/ringbuf.c
+CC ../../py/cstack.c
+CC ../../py/stackctrl.c
+CC ../../py/argcheck.c
+CC ../../py/warning.c
+CC ../../py/profile.c
+CC ../../py/map.c
+CC ../../py/obj.c
+CC ../../py/objarray.c
+CC ../../py/objattrtuple.c
+CC ../../py/objbool.c
+CC ../../py/objboundmeth.c
+CC ../../py/objcell.c
+CC ../../py/objclosure.c
+CC ../../py/objcomplex.c
+CC ../../py/objdeque.c
+CC ../../py/objdict.c
+CC ../../py/objenumerate.c
+CC ../../py/objexcept.c
+CC ../../py/objfilter.c
+CC ../../py/objfloat.c
+CC ../../py/objfun.c
+CC ../../py/objgenerator.c
+CC ../../py/objgetitemiter.c
+CC ../../py/objint.c
+CC ../../py/objint_longlong.c
+CC ../../py/objint_mpz.c
+CC ../../py/objlist.c
+CC ../../py/objmap.c
+CC ../../py/objmodule.c
+CC ../../py/objobject.c
+CC ../../py/objpolyiter.c
+CC ../../py/objproperty.c
+CC ../../py/objnone.c
+CC ../../py/objnamedtuple.c
+CC ../../py/objrange.c
+CC ../../py/objreversed.c
+CC ../../py/objringio.c
+CC ../../py/objset.c
+CC ../../py/objsingleton.c
+CC ../../py/objslice.c
+CC ../../py/objstr.c
+CC ../../py/objstrunicode.c
+CC ../../py/objstringio.c
+CC ../../py/objtuple.c
+CC ../../py/objtype.c
+CC ../../py/objzip.c
+CC ../../py/opmethods.c
+CC ../../py/sequence.c
+CC ../../py/stream.c
+CC ../../py/binary.c
+CC ../../py/builtinimport.c
+CC ../../py/builtinevex.c
+CC ../../py/builtinhelp.c
+CC ../../py/modarray.c
+CC ../../py/modbuiltins.c
+CC ../../py/modcollections.c
+CC ../../py/modgc.c
+CC ../../py/modio.c
+CC ../../py/modmath.c
+CC ../../py/modcmath.c
+CC ../../py/modmicropython.c
+CC ../../py/modstruct.c
+CC ../../py/modsys.c
+CC ../../py/moderrno.c
+CC ../../py/modthread.c
+CC ../../py/vm.c
+CC ../../py/bc.c
+CC ../../py/showbc.c
+CC ../../py/repl.c
+CC ../../py/smallint.c
+CC ../../py/frozenmod.c
+CC build-VK_RA6M5/lvgl/lv_mpy.c
+CC ../../extmod/machine_adc.c
+CC ../../extmod/machine_adc_block.c
+CC ../../extmod/machine_bitstream.c
+CC ../../extmod/machine_i2c.c
+CC ../../extmod/machine_i2s.c
+CC ../../extmod/machine_mem.c
+CC ../../extmod/machine_pinbase.c
+CC ../../extmod/machine_pulse.c
+CC ../../extmod/machine_pwm.c
+CC ../../extmod/machine_signal.c
+CC ../../extmod/machine_spi.c
+CC ../../extmod/machine_timer.c
+CC ../../extmod/machine_uart.c
+CC ../../extmod/machine_usb_device.c
+CC ../../extmod/machine_wdt.c
+CC ../../extmod/modasyncio.c
+CC ../../extmod/modbinascii.c
+CC ../../extmod/modbluetooth.c
+CC ../../extmod/modbtree.c
+CC ../../extmod/modcryptolib.c
+CC ../../extmod/moddeflate.c
+CC ../../extmod/modframebuf.c
+CC ../../extmod/modhashlib.c
+CC ../../extmod/modheapq.c
+CC ../../extmod/modjson.c
+CC ../../extmod/modlwip.c
+CC ../../extmod/modmachine.c
+CC ../../extmod/modnetwork.c
+CC ../../extmod/modonewire.c
+CC ../../extmod/modos.c
+CC ../../extmod/modplatform.c
+CC ../../extmod/modrandom.c
+CC ../../extmod/modre.c
+CC ../../extmod/modselect.c
+CC ../../extmod/modsocket.c
+CC ../../extmod/modtls_axtls.c
+CC ../../extmod/modtls_mbedtls.c
+CC ../../extmod/modtime.c
+CC ../../extmod/moductypes.c
+CC ../../extmod/modvfs.c
+CC ../../extmod/modwebrepl.c
+CC ../../extmod/modwebsocket.c
+CC ../../extmod/network_cyw43.c
+CC ../../extmod/network_esp_hosted.c
+CC ../../extmod/network_lwip.c
+CC ../../extmod/network_ninaw10.c
+CC ../../extmod/network_ppp_lwip.c
+CC ../../extmod/network_wiznet5k.c
+CC ../../extmod/os_dupterm.c
+CC ../../extmod/vfs.c
+CC ../../extmod/vfs_blockdev.c
+CC ../../extmod/vfs_fat.c
+CC ../../extmod/vfs_fat_diskio.c
+CC ../../extmod/vfs_fat_file.c
+CC ../../extmod/vfs_lfs.c
+CC ../../extmod/vfs_posix.c
+CC ../../extmod/vfs_posix_file.c
+CC ../../extmod/vfs_reader.c
+CC ../../extmod/virtpin.c
+CC ../../shared/libc/abort_.c
+CC ../../shared/libc/printf.c
+CC ../../lib/oofatfs/ff.c
+CC ../../lib/oofatfs/ffunicode.c
+CC ../../shared/netutils/netutils.c
+CC mbedtls/mbedtls_port.c
+CC ../../shared/netutils/dhcpserver.c
+CC ../../shared/netutils/trace.c
+CC ../../shared/readline/readline.c
+CC ../../shared/runtime/gchelper_native.c
+CC ../../shared/runtime/interrupt_char.c
+CC ../../shared/runtime/mpirq.c
+CC ../../shared/runtime/pyexec.c
+CC ../../shared/runtime/softtimer.c
+CC ../../shared/runtime/stdout_helpers.c
+CC ../../shared/runtime/sys_stdio_mphal.c
+CC ../../shared/timeutils/timeutils.c
+CC ../../shared/tinyusb/mp_usbd.c
+CC ../../shared/tinyusb/mp_usbd_cdc.c
+CC ../../shared/tinyusb/mp_usbd_descriptor.c
+CC ../../drivers/bus/softspi.c
+CC ../../drivers/bus/softqspi.c
+CC ../../drivers/memory/spiflash.c
+CC ../../drivers/dht/dht.c
+CC ra/ra_adc.c
+CC ra/ra_dac.c
+CC ra/ra_flash.c
+CC ra/ra_gpio.c
+CC ra/ra_i2c.c
+CC ra/ra_icu.c
+CC ra/ra_init.c
+CC ra/ra_int.c
+CC ra/ra_rtc.c
+CC ra/ra_sci.c
+CC ra/ra_spi.c
+CC ra/ra_timer.c
+CC ra/ra_gpt.c
+CC ra/ra_utils.c
+CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
+CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
+CC ../../lib/tinyusb/src/class/hid/hid_device.c
+CC ../../lib/tinyusb/src/class/midi/midi_device.c
+CC ../../lib/tinyusb/src/class/msc/msc_device.c
+CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
+CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
+CC ../../lib/tinyusb/src/common/tusb_fifo.c
+CC ../../lib/tinyusb/src/device/usbd.c
+CC ../../lib/tinyusb/src/device/usbd_control.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
+CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
+CC ../../lib/tinyusb/src/tusb.c
+CC boardctrl.c
+CC main.c
+CC ra_it.c
+CC rng.c
+CC mphalport.c
+CC mpnetworkport.c
+CC mpthreadport.c
+CC irq.c
+CC pendsv.c
+CC systick.c
+CC powerctrl.c
+CC powerctrlboot.c
+CC pybthread.c
+CC factoryreset.c
+CC timer.c
+CC led.c
+CC uart.c
+CC gccollect.c
+CC help.c
+CC machine_dac.c
+CC machine_i2c.c
+CC machine_spi.c
+CC machine_pin.c
+CC machine_rtc.c
+CC machine_sdcard.c
+CC network_lan.c
+CC eth.c
+CC extint.c
+CC usrsw.c
+CC flash.c
+CC flashbdev.c
+CC storage.c
+CC fatfs_port.c
+CC usbd.c
+CC boards/VK_RA6M5/board_init.c
+CC build-VK_RA6M5/pins_VK_RA6M5.c
+CC build-VK_RA6M5/frozen_content.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222020.log b/ports/renesas-ra/VK_RA6M5_build_202506222020.log
new file mode 100644
index 00000000..7f2de807
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222020.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222023.log b/ports/renesas-ra/VK_RA6M5_build_202506222023.log
new file mode 100644
index 00000000..6f4cce8f
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222023.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC main.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222033.log b/ports/renesas-ra/VK_RA6M5_build_202506222033.log
new file mode 100644
index 00000000..af03ac22
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222033.log
@@ -0,0 +1,25 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC main.c
+../../py/gc_ospi.c: In function 'gc_ospi_reset':
+../../py/gc_ospi.c:465:5: error: implicit declaration of function 'mp_hal_stdout_tx_flush'; did you mean 'mp_hal_stdout_tx_strn'? [-Werror=implicit-function-declaration]
+  465 |     mp_hal_stdout_tx_flush();   /* flush debug съобщенията */
+      |     ^~~~~~~~~~~~~~~~~~~~~~
+      |     mp_hal_stdout_tx_strn
+cc1.exe: all warnings being treated as errors
+See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
+make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/py/gc_ospi.o] Error 1
+make: *** Waiting for unfinished jobs....
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222034.log b/ports/renesas-ra/VK_RA6M5_build_202506222034.log
new file mode 100644
index 00000000..7f2de807
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222034.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+QSTR not updated
+Module registrations not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222054.log b/ports/renesas-ra/VK_RA6M5_build_202506222054.log
new file mode 100644
index 00000000..9878c9de
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222054.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+Root pointer registrations not updated
+QSTR not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222058.log b/ports/renesas-ra/VK_RA6M5_build_202506222058.log
new file mode 100644
index 00000000..c8da94ae
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222058.log
@@ -0,0 +1,20 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+CC main.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/VK_RA6M5_build_202506222102.log b/ports/renesas-ra/VK_RA6M5_build_202506222102.log
new file mode 100644
index 00000000..6f459a13
--- /dev/null
+++ b/ports/renesas-ra/VK_RA6M5_build_202506222102.log
@@ -0,0 +1,19 @@
+Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
+GEN build-VK_RA6M5/genhdr/qstr.i.last
+GEN build-VK_RA6M5/genhdr/qstr.split
+GEN build-VK_RA6M5/genhdr/moduledefs.split
+GEN build-VK_RA6M5/genhdr/root_pointers.split
+GEN build-VK_RA6M5/genhdr/compressed.split
+GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
+GEN build-VK_RA6M5/genhdr/moduledefs.collected
+GEN build-VK_RA6M5/genhdr/root_pointers.collected
+GEN build-VK_RA6M5/genhdr/compressed.collected
+Module registrations not updated
+QSTR not updated
+Root pointer registrations not updated
+Compressed data not updated
+CC ../../py/gc_ospi.c
+  GEN     objects.rsp
+LINK build-VK_RA6M5/firmware.elf
+GEN build-VK_RA6M5/firmware.hex
+GEN build-VK_RA6M5/firmware.bin
diff --git a/ports/renesas-ra/boards/VK_RA6M5/board_init.c b/ports/renesas-ra/boards/VK_RA6M5/board_init.c
new file mode 100644
index 00000000..0060d6df
--- /dev/null
+++ b/ports/renesas-ra/boards/VK_RA6M5/board_init.c
@@ -0,0 +1,53 @@
+/*
+ * This file is part of the MicroPython project, http://micropython.org/
+ *
+ * The MIT License (MIT)
+ *
+ * Copyright (c) 2021 Renesas Electronics Corporation
+ *
+ * Permission is hereby granted, free of charge, to any person obtaining a copy
+ * of this software and associated documentation files (the "Software"), to deal
+ * in the Software without restriction, including without limitation the rights
+ * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+ * copies of the Software, and to permit persons to whom the Software is
+ * furnished to do so, subject to the following conditions:
+ *
+ * The above copyright notice and this permission notice shall be included in
+ * all copies or substantial portions of the Software.
+ *
+ * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+ * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+ * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+ * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+ * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+ * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
+ * THE SOFTWARE.
+ */
+
+#include "py/mphal.h"
+#include "hal_data.h"
+
+#if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+#include "py/gc_ospi.h"
+#endif
+
+// Board-specific initialization
+void board_init(void) {
+    // Initialize clocks, pins, etc. (if needed)
+    
+    #if MICROPY_HW_HAS_OSPI_RAM
+    // Initialize OSPI RAM hardware
+    printf("[BOARD] Initializing OSPI RAM...\n");
+    fsp_err_t ospi_result = g_ospi_ram0.p_api->open(g_ospi_ram0.p_ctrl, g_ospi_ram0.p_cfg);
+    if (FSP_SUCCESS == ospi_result) {
+        printf("[BOARD] OSPI RAM hardware initialized successfully\n");
+        
+        #if MICROPY_ENABLE_GC
+        // OSPI GC heap will be initialized later in main.c after gc_init()
+        printf("[BOARD] OSPI RAM hardware ready for GC integration\n");
+        #endif
+    } else {
+        printf("[BOARD] OSPI RAM hardware initialization failed with error %d\n", ospi_result);
+    }
+    #endif
+}
diff --git a/ports/renesas-ra/boards/VK_RA6M5/mpconfigboard.h b/ports/renesas-ra/boards/VK_RA6M5/mpconfigboard.h
index 9b59c506..902fc485 100644
--- a/ports/renesas-ra/boards/VK_RA6M5/mpconfigboard.h
+++ b/ports/renesas-ra/boards/VK_RA6M5/mpconfigboard.h
@@ -27,7 +27,7 @@
 #define MICROPY_HW_ENABLE_INTERNAL_FLASH_STORAGE (1)
 #define MICROPY_HW_HAS_QSPI_FLASH   (1)
 #define MICROPY_HW_HAS_SDHI_CARD    (1)
-#define MICROPY_HW_HAS_OSPI_RAM     (1)
+#define MICROPY_HW_HAS_OSPI_RAM     (1)  // Включено за debug
 
 // board config
 
diff --git a/ports/renesas-ra/lextab.py b/ports/renesas-ra/lextab.py
index 39685833..a69ace92 100644
--- a/ports/renesas-ra/lextab.py
+++ b/ports/renesas-ra/lextab.py
@@ -1,6 +1,6 @@
 # lextab.py. This file automatically created by PLY (version 3.10). Don't edit!
 _tabversion   = '3.10'
-_lextokens    = set(('TIMESEQUAL', 'COLON', 'MOD', 'PLUS', 'CHAR_CONST', 'CONDOP', 'XOR', 'OR', 'REGISTER', 'GE', 'COMMA', 'LPAREN', '_STATIC_ASSERT', 'SEMI', 'INT_CONST_HEX', 'PPPRAGMA', 'LSHIFT', 'INT_CONST_DEC', 'WHILE', 'WCHAR_CONST', 'ARROW', 'LSHIFTEQUAL', 'PLUSPLUS', 'ELSE', 'INT_CONST_OCT', 'STATIC', 'SIZEOF', '__INT128', 'DEFAULT', 'TYPEDEF', 'HEX_FLOAT_CONST', 'MINUS', 'U8STRING_LITERAL', 'RETURN', 'RBRACE', 'EQ', 'OREQUAL', 'ELLIPSIS', 'MINUSMINUS', 'UNION', 'DO', 'DIVEQUAL', '_THREAD_LOCAL', 'LNOT', 'U32CHAR_CONST', 'PLUSEQUAL', 'AUTO', 'AND', 'GOTO', 'EXTERN', 'STRING_LITERAL', 'INT_CONST_BIN', 'EQUALS', '_BOOL', 'MODEQUAL', 'DOUBLE', 'LE', 'XOREQUAL', 'SHORT', 'VOID', '_ALIGNOF', 'CONTINUE', 'FLOAT', 'IF', 'GT', 'LBRACKET', 'VOLATILE', '_NORETURN', 'CONST', 'SIGNED', '_ATOMIC', 'TIMES', 'SWITCH', '_COMPLEX', 'MINUSEQUAL', 'INLINE', 'TYPEID', 'LOR', 'U16STRING_LITERAL', 'NE', 'RESTRICT', 'RSHIFTEQUAL', 'ID', 'NOT', '_ALIGNAS', 'DIVIDE', 'LONG', 'PPPRAGMASTR', 'WSTRING_LITERAL', 'PERIOD', 'FLOAT_CONST', 'U8CHAR_CONST', 'ANDEQUAL', 'ENUM', 'FOR', 'LAND', 'LT', 'CHAR', 'INT_CONST_CHAR', 'BREAK', 'RPAREN', 'RSHIFT', 'CASE', 'U32STRING_LITERAL', 'PPHASH', 'OFFSETOF', 'UNSIGNED', 'LBRACE', 'STRUCT', 'RBRACKET', 'U16CHAR_CONST', 'INT'))
+_lextokens    = set(('MINUSMINUS', 'RBRACE', '__INT128', 'STRUCT', '_COMPLEX', 'DEFAULT', 'NE', 'RPAREN', 'OREQUAL', 'UNSIGNED', 'RSHIFT', 'WHILE', 'CONST', 'AND', 'INT_CONST_HEX', '_ALIGNOF', 'SWITCH', 'PLUSEQUAL', 'UNION', 'LPAREN', 'WSTRING_LITERAL', 'INLINE', 'XOR', 'LE', 'COLON', 'FLOAT', '_STATIC_ASSERT', 'ENUM', 'DIVEQUAL', 'OFFSETOF', 'INT_CONST_OCT', 'FOR', 'GE', 'IF', 'LAND', 'AUTO', 'DOUBLE', 'INT_CONST_DEC', 'LSHIFT', 'DIVIDE', 'BREAK', 'VOLATILE', 'TIMESEQUAL', 'COMMA', 'PPPRAGMASTR', 'U16STRING_LITERAL', 'FLOAT_CONST', 'CONDOP', 'TIMES', 'ELSE', 'MINUSEQUAL', 'U32CHAR_CONST', 'MOD', 'U8CHAR_CONST', 'MODEQUAL', 'SIGNED', 'LOR', 'LNOT', 'EQUALS', 'SHORT', 'INT_CONST_BIN', 'GT', 'PERIOD', 'DO', 'XOREQUAL', 'ELLIPSIS', 'RBRACKET', '_ALIGNAS', 'CASE', 'VOID', 'RESTRICT', 'PPHASH', 'LBRACE', 'ID', 'LBRACKET', 'PLUSPLUS', '_THREAD_LOCAL', '_ATOMIC', 'CHAR', 'SEMI', 'REGISTER', 'EQ', 'STRING_LITERAL', 'LSHIFTEQUAL', 'U8STRING_LITERAL', 'SIZEOF', 'WCHAR_CONST', 'LONG', 'ARROW', 'MINUS', 'RSHIFTEQUAL', 'HEX_FLOAT_CONST', 'INT', 'NOT', 'INT_CONST_CHAR', 'U16CHAR_CONST', 'TYPEDEF', 'OR', 'PPPRAGMA', 'TYPEID', 'EXTERN', 'CONTINUE', 'GOTO', 'LT', 'ANDEQUAL', 'PLUS', 'RETURN', 'CHAR_CONST', 'STATIC', 'U32STRING_LITERAL', '_NORETURN', '_BOOL'))
 _lexreflags   = 64
 _lexliterals  = ''
 _lexstateinfo = {'INITIAL': 'inclusive', 'ppline': 'exclusive', 'pppragma': 'exclusive'}
diff --git a/ports/renesas-ra/main.c b/ports/renesas-ra/main.c
index 9ea29713..be2d5ccf 100644
--- a/ports/renesas-ra/main.c
+++ b/ports/renesas-ra/main.c
@@ -323,6 +323,7 @@ soft_reset:
     // GC init
     #if MICROPY_ENABLE_GC
     gc_init(MICROPY_HEAP_START, MICROPY_HEAP_END);
+// OSPI GC инициализация отложена до по-късно в boot процеса
     #if MICROPY_GC_SPLIT_HEAP
     assert(MICROPY_GC_SPLIT_HEAP_N_HEAPS > 0);
     #if MICROPY_HW_HAS_OSPI_RAM
@@ -334,9 +335,16 @@ soft_reset:
                (void*)&_octa_ram_start, (void*)&_octa_ram_end,
                (unsigned long)((char*)&_octa_ram_end - (char*)&_octa_ram_start));
 
-        // Add OSPI RAM to GC heap with proper clamping
+        // Дай време на OSPI hardware да се стабилизира
+        printf("[OSPI] Waiting for OSPI hardware to stabilize...\n");
+        mp_hal_delay_ms(100);  // Увеличен delay
+
+        // Сега когато OSPI hardware е готов, добавяме го към GC
+        #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+        printf("[OSPI] Adding OSPI RAM to GC heap...\n");
         gc_ospi_init();
         printf("[OSPI] GC heap extended with OSPI RAM\n");
+        #endif
     } else {
         printf("[OSPI] FAILED: OSPI RAM open failed with error %d\n", ospi_result);
     }
@@ -482,6 +490,11 @@ soft_reset_exit:
 
     MICROPY_BOARD_END_SOFT_RESET(&state);
 
+    #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
+    // Reset OSPI GC state for next boot
+    gc_ospi_reset();
+    #endif
+
     gc_sweep_all();
     mp_deinit();
 
diff --git a/py/gc_ospi.c b/py/gc_ospi.c
index 71477911..e502c92a 100644
--- a/py/gc_ospi.c
+++ b/py/gc_ospi.c
@@ -7,6 +7,10 @@
  */
 
 #include "py/mpconfig.h"
+
+/* bring OSPI_START/END and other internals */
+#include "py/gc_ospi_internal.h"
+
 #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
 
 #include <string.h>
@@ -14,14 +18,52 @@
 #include "py/mpstate.h"
 #include "py/runtime.h"
 #include "py/gc_ospi.h"          // Public OSPI GC interface
-#include "py/gc_ospi_internal.h" // Internal OSPI GC definitions
 #include "py/ospi_dma.h"         // DMA cache coherency helpers
 
+#ifdef BSP_DELAY_UNITS_MICROSECONDS   /* идва от fsp/bsp_api.h */
+#include "bsp_api.h"
+#define OSPI_HAVE_FSP  1
+#else
+#define OSPI_HAVE_FSP  0
+#endif
+
+/* Set to 1 **only** if/когато имате пълно, safe forwarding на всички
+ * указатели (т.е. реален moving GC).  Засега оставяме 0. */
+#ifndef OSPI_ENABLE_COMPACTION
+#define OSPI_ENABLE_COMPACTION   (0)
+#endif
+
+
+
+#define OSPI_COPY_CHUNK  (16 * 1024)   /* 16 KB */
+
+/* Safe copy – разделя >64 KB на по-малки блокове; между тях кратка пауза
+ * за да даде шанс на DTC/DMAC и кеш-coherency логика.                */
+static inline void ospi_chunk_copy(void *dst, const void *src, size_t len)
+{
+    if (len <= 64 * 1024) {          /* малко – директно memcpy */
+        memcpy(dst, src, len);
+        return;
+    }
+
+    const uint8_t *s = (const uint8_t *)src;
+    uint8_t       *d = (uint8_t *)dst;
+    while (len) {
+        size_t step = len > OSPI_COPY_CHUNK ? OSPI_COPY_CHUNK : len;
+        memcpy(d, s, step);
+#if OSPI_HAVE_FSP
+        /* 1 µs е достатъчно да "пусне" DMA-апарата (≈8 µs/MB @120 MHz) */
+        R_BSP_SoftwareDelay(1, BSP_DELAY_UNITS_MICROSECONDS);
+#endif
+        s   += step;
+        d   += step;
+        len -= step;
+    }
+}
+
 //─────────────────────────────────────────────────────────────
 // Configuration
 //─────────────────────────────────────────────────────────────
-#define OSPI_START      0x68000000u
-#define OSPI_END        0x68800000u
 #define OSPI_THRESHOLD  (128 * 1024)      // ≥128KB → OSPI
 
 // Overflow protection for free block merging
@@ -74,6 +116,58 @@
     ((void*)((uintptr_t)(area)->gc_pool_start + (block) * BYTES_PER_BLOCK))
 #endif
 
+/* ATB helpers */
+#ifndef atb_is_free
+#define atb_is_free(area, blk)  (ATB_GET_KIND((area), (blk)) == AT_FREE)
+#endif
+
+/* ----------------------------------------------------------+
+ |  Light-weight stats helpers — трябва да са видими преди   |
+ |  gc_ospi_stats_core(), в противен случай получаваме       |
+ |  "unknown type / implicit declaration".                   |
+ +----------------------------------------------------------*/
+typedef struct {
+    size_t total_blocks;
+    size_t free_blocks;
+    size_t free_chunks;
+    size_t largest_free;
+} ospi_stat_t;
+
+/* forward-decl – използва се по-рано в кода                    */
+static void ospi_collect_stats(mp_state_mem_area_t *area, ospi_stat_t *st);
+
+/* ───────────────────────────────────────────────────────
+ *  Implementation
+ * ───────────────────────────────────────────────────────*/
+
+static void ospi_collect_stats(mp_state_mem_area_t *area, ospi_stat_t *st)
+{
+    st->total_blocks = area->gc_alloc_table_byte_len * BLOCKS_PER_ATB;
+    st->free_blocks  = 0;
+    st->free_chunks  = 0;
+    st->largest_free = 0;
+
+    size_t run = 0;
+    for (size_t i = 0; i < st->total_blocks; i++) {
+        if (atb_is_free(area, i)) {
+            st->free_blocks++;
+            run++;
+        } else if (run) {
+            st->free_chunks++;
+            if (run > st->largest_free) {
+                st->largest_free = run;
+            }
+            run = 0;
+        }
+    }
+    if (run) {
+        st->free_chunks++;
+        if (run > st->largest_free) {
+            st->largest_free = run;
+        }
+    }
+}
+
 //─────────────────────────────────────────────────────────────
 // Debug & locking
 //─────────────────────────────────────────────────────────────
@@ -98,15 +192,28 @@ extern void *gc_realloc_core(void *, size_t, bool);
 #endif
 
 //─────────────────────────────────────────────────────────────
-// OSPI area detection
+// OSPI area detection with lazy cache
 //─────────────────────────────────────────────────────────────
+
+/* one-time cache; NULL ⇒ not yet searched, (void*)-1 ⇒ not present */
+static mp_state_mem_area_t *cached_ospi_area = NULL;
+
 static mp_state_mem_area_t *find_ospi_area(void) {
+    if (cached_ospi_area == (mp_state_mem_area_t *)-1) {
+        return NULL;                        /* вече знаем, че липсва */
+    }
+    if (cached_ospi_area) {
+        return cached_ospi_area;            /* удар в кеша */
+    }
+
     for (mp_state_mem_area_t *a = &MP_STATE_MEM(area); a; a = NEXT_AREA(a)) {
         uintptr_t s = (uintptr_t)a->gc_pool_start;
         if (s >= OSPI_START && s < OSPI_END) {
-            return a;
+            return cached_ospi_area = a;    /* кешираме и връщаме */
         }
     }
+
+    cached_ospi_area = (mp_state_mem_area_t *)-1;   /* окончателно няма */
     return NULL;
 }
 
@@ -133,96 +240,9 @@ static inline void ospi_set_free(mp_state_mem_area_t *area, size_t start_block,
     });
 }
 
-//─────────────────────────────────────────────────────────────
-// OSPI allocation (first-fit)
-//─────────────────────────────────────────────────────────────
-static void *ospi_alloc_from_area(mp_state_mem_area_t *a,
-                                  size_t n_blocks, bool clear) {
-    // CORRECT: 4 blocks per ATB byte (2 bits per block)
-    const size_t total_blocks = a->gc_alloc_table_byte_len * BLOCKS_PER_ATB;
-    
-    // First-fit search
-    size_t cur_start = 0, cur_len = 0;
-    
-    for (size_t b = 0; b < total_blocks; ++b) {
-        if (ATB_GET_KIND(a, b) == AT_FREE) {
-            if (cur_len == 0) {
-                cur_start = b;
-            }
-            cur_len++;
-            if (cur_len == n_blocks) {
-                // Found suitable block
-                break;
-            }
-        } else {
-            cur_len = 0;
-        }
-    }
-    
-    if (cur_len < n_blocks) {
-        return NULL;  // No suitable block found
-    }
-    
-    // Mark blocks as allocated (IRQ-safe)
-    ospi_set_used(a, cur_start, n_blocks);
-    
-    // Update last used block (IRQ-safe)
-    OSPI_GC_ATOMIC({
-        if (cur_start + n_blocks - 1 > a->gc_last_used_block) {
-            a->gc_last_used_block = cur_start + n_blocks - 1;
-        }
-    });
-    
-    void *ptr = PTR_FROM_BLOCK(a, cur_start);
-    if (clear) {
-        memset(ptr, 0, n_blocks * BYTES_PER_BLOCK);
-    }
-
-    // DMA cache coherency: clean cache lines for allocated region
-    ospi_cache_clean(ptr, n_blocks * BYTES_PER_BLOCK);
-
-    return ptr;
-}
-
-//─────────────────────────────────────────────────────────────
-// gc_alloc override
-//─────────────────────────────────────────────────────────────
-void *gc_alloc(size_t n_bytes, unsigned int flags) {
-    if (n_bytes >= OSPI_THRESHOLD) {
-        mp_state_mem_area_t *ospi_area = find_ospi_area();
-        if (ospi_area) {
-            bool clear = !(flags & GC_ALLOC_FLAG_HAS_FINALISER);
-            size_t n_blocks = (n_bytes + BYTES_PER_BLOCK - 1) / BYTES_PER_BLOCK;
-            
-            GC_ENTER();
-            void *ptr = ospi_alloc_from_area(ospi_area, n_blocks, clear);
-            GC_EXIT();
-            
-            if (ptr) {
-                mp_printf(&mp_plat_print, "[OSPI_GC] Alloc %u bytes -> OSPI @%p\n",
-                          (unsigned)n_bytes, ptr);
-                return ptr;
-            }
-        }
-    }
-    return gc_alloc_core(n_bytes, flags);
-}
 
-//─────────────────────────────────────────────────────────────
-// gc_free override - delegate to core
-//─────────────────────────────────────────────────────────────
-void gc_free(void *ptr) {
-    // Core GC knows how to handle OSPI pointers via ATB
-    gc_free_core(ptr);
-}
 
-//─────────────────────────────────────────────────────────────
-// gc_realloc override - handle OSPI pointers
-//─────────────────────────────────────────────────────────────
-void *gc_realloc(void *ptr, size_t n_bytes, bool allow_move) {
-    // For OSPI pointers, core GC should handle via ATB
-    return gc_realloc_core(ptr, n_bytes, allow_move);
-}
+/* НЯМА override – split-heap на ядрото избира правилния регион         */
 
 //─────────────────────────────────────────────────────────────
 // Stats function for diagnostics
@@ -234,37 +254,27 @@ static void gc_ospi_stats_core(void) {
         return;
     }
 
-    const size_t total = a->gc_alloc_table_byte_len * BLOCKS_PER_ATB;
-    size_t used = 0, free = 0, chunks = 0;
-    bool in_free_chunk = false;
-
-    for (size_t b = 0; b < total; ++b) {
-        switch (ATB_GET_KIND(a, b)) {
-            case AT_FREE:
-                free++;
-                if (!in_free_chunk) {
-                    chunks++;
-                    in_free_chunk = true;
-                }
-                break;
-            default:
-                used++;
-                in_free_chunk = false;
-                break;
-        }
-    }
+    ospi_stat_t st;
+    ospi_collect_stats(a, &st);
+
+    size_t used = st.total_blocks - st.free_blocks;
+    /* нова, по-смислена метрика: (chunks-1)/free_blocks */
+    uint32_t frag_pct = (st.free_blocks > 0 && st.free_chunks > 1)
+        ? (100U * (st.free_chunks - 1)) / st.free_blocks
+        : 0;
 
-    uint32_t frag_pct = (chunks > 1) ? (100 * (chunks - 1)) / chunks : 0;
-
-    mp_printf(&mp_plat_print, "[OSPI_GC] === Statistics ===\n");
-    mp_printf(&mp_plat_print, "Total blocks: %u (%u KB)\n",
-              (unsigned)total, (unsigned)(total * BYTES_PER_BLOCK / 1024));
-    mp_printf(&mp_plat_print, "Used: %u blocks (%u KB)\n",
-              (unsigned)used, (unsigned)(used * BYTES_PER_BLOCK / 1024));
-    mp_printf(&mp_plat_print, "Free: %u blocks (%u KB)\n",
-              (unsigned)free, (unsigned)(free * BYTES_PER_BLOCK / 1024));
-    mp_printf(&mp_plat_print, "Free chunks: %u → Fragmentation: %u%%\n",
-              (unsigned)chunks, frag_pct);
+    mp_printf(&mp_plat_print,
+        "[OSPI_GC] === Statistics ===\n"
+        "Total blocks: %u (%u KB)\n"
+        "Used:  %u blocks (%u KB)\n"
+        "Free:  %u blocks (%u KB)\n"
+        "Free chunks: %u → Fragmentation: %u %%\n"
+        "Largest free run: %u blocks (%u KB)\n",
+        (unsigned)st.total_blocks, (unsigned)(st.total_blocks*BYTES_PER_BLOCK/1024),
+        (unsigned)used,             (unsigned)(used*BYTES_PER_BLOCK/1024),
+        (unsigned)st.free_blocks,   (unsigned)(st.free_blocks*BYTES_PER_BLOCK/1024),
+        (unsigned)st.free_chunks,   (unsigned)frag_pct,
+        (unsigned)st.largest_free,  (unsigned)(st.largest_free*BYTES_PER_BLOCK/1024));
 }
 
 //─────────────────────────────────────────────────────────────
@@ -298,7 +308,10 @@ void gc_ospi_set_strategy(int strategy) {
     (((uintptr_t)(ptr) - (uintptr_t)(area)->gc_pool_start) / BYTES_PER_BLOCK)
 #endif
 
-// Get allocation size in blocks
+/* get_allocation_size() is only required when defrag is compiled in   */
+#if OSPI_STATS_ONLY
+__attribute__((unused))
+#endif
 static size_t get_allocation_size(mp_state_mem_area_t *area, size_t block) {
     size_t n_blocks = 0;
     size_t b = block;
@@ -317,6 +330,18 @@ static size_t get_allocation_size(mp_state_mem_area_t *area, size_t block) {
 
 // Safe defragmentation that preserves Python references
 void gc_ospi_defrag(void) {
+#if !OSPI_ENABLE_COMPACTION
+    /* ----------------------------------------------------------------
+     *  SAFE-MODE:  само статистика + принудително събиране на боклука
+     * ---------------------------------------------------------------- */
+    mp_printf(&mp_plat_print,
+        "[OSPI_DEFRAG] Compaction is disabled (OSPI_ENABLE_COMPACTION=0)\n");
+
+    extern void gc_collect(void);
+    gc_collect();           /* 1× пълен GC – често е напълно достатъчно */
+    gc_ospi_stats_core();   /* и след това статистика */
+    return;
+#else
     mp_state_mem_area_t *ospi_area = find_ospi_area();
     if (!ospi_area) {
         mp_printf(&mp_plat_print, "[OSPI_DEFRAG] No OSPI area found\n");
@@ -386,8 +411,8 @@ void gc_ospi_defrag(void) {
                     void *new_ptr = PTR_FROM_BLOCK(ospi_area, write_block);
                     size_t size = n_blocks * BYTES_PER_BLOCK;
 
-                    // Move the data
-                    memmove(new_ptr, old_ptr, size);
+                    // Move the data (chunked copy for large blocks)
+                    ospi_chunk_copy(new_ptr, old_ptr, size);
 
                     // Update ATB for new location (IRQ-safe)
                     ospi_set_used(ospi_area, write_block, n_blocks);
@@ -422,23 +447,48 @@ void gc_ospi_defrag(void) {
 
     // Show stats after defrag
     gc_ospi_stats_core();
+#endif
 }
 
 //─────────────────────────────────────────────────────────────
 // Initialization functions
 //─────────────────────────────────────────────────────────────
 
+static bool ospi_heap_added = false;     /* NEW – глобално знаме */
+
+// Reset function for soft reset
+void gc_ospi_reset(void) {
+    mp_printf(&mp_plat_print, "[OSPI_GC] gc_ospi_reset() called - preparing for soft reboot\n");
+    ospi_heap_added = false;    /* reset флаг за следващия boot */
+    cached_ospi_area = NULL;    /* reset cache за следващия boot */
+    mp_printf(&mp_plat_print, "[OSPI_GC] gc_ospi_reset() completed\n");
+}
+
 // Initialize OSPI area with size clamping (fix for linker/FSP oversize issues)
 void gc_ospi_init(void) {
+    mp_printf(&mp_plat_print, "[OSPI_GC] gc_ospi_init() called\n");
+
+    /*  ── 1. Вече регистриранa? Проверяваме чрез find_ospi_area()  ─────────────────────────── */
+    mp_state_mem_area_t *existing_area = find_ospi_area();
+    if (existing_area != NULL) {
+        mp_printf(&mp_plat_print, "[OSPI_GC] OSPI heap already exists in GC at %p, skipping init\n",
+                  existing_area->gc_pool_start);
+        ospi_heap_added = true;  /* синхронизираме флага */
+        return;                  /* идемпотентност – прави нищи */
+    }
+
+    mp_printf(&mp_plat_print, "[OSPI_GC] No OSPI heap added yet, initializing...\n");
+
     // Get linker symbols (may be oversized by FSP)
     extern uint8_t _octa_ram_start, _octa_ram_end;
     uintptr_t start_addr = (uintptr_t)&_octa_ram_start;
     uintptr_t end_addr = (uintptr_t)&_octa_ram_end;
 
-    size_t bytes = end_addr - start_addr;
+    mp_printf(&mp_plat_print, "[OSPI_GC] Linker symbols: start=0x%08x, end=0x%08x\n",
+              (unsigned)start_addr, (unsigned)end_addr);
 
-    /* Align region size down to whole GC blocks (256 B)               */
-    bytes &= ~(BYTES_PER_BLOCK - 1);   /* guard against odd linker size */
+    /* Align region size down to whole GC blocks (256 B) - improved version */
+    size_t bytes = (end_addr - start_addr) & ~(BYTES_PER_BLOCK - 1u);
 
     size_t original_bytes = bytes;
 
@@ -450,10 +500,28 @@ void gc_ospi_init(void) {
                   (unsigned)original_bytes);
     }
 
-    // Add the clamped OSPI region to GC heap
+    // OSPI RAM не поддържа memory mapped mode в FSP
+    // Трябва да използваме OSPI API за read/write операции
+    mp_printf(&mp_plat_print, "[OSPI_GC] WARNING: OSPI RAM не поддържа memory mapped mode\n");
+    mp_printf(&mp_plat_print, "[OSPI_GC] Пропускаме OSPI GC инициализация - използвайте само SRAM\n");
+    return;
+    mp_printf(&mp_plat_print, "[OSPI_GC] About to call gc_add(0x%08x, 0x%08x)\n",
+              (unsigned)start_addr, (unsigned)(start_addr + bytes));
+
     extern void gc_add(void *start, void *end);
     gc_add((void *)start_addr, (void *)(start_addr + bytes));
 
+    mp_printf(&mp_plat_print, "[OSPI_GC] gc_add() completed successfully\n");
+
+    ospi_heap_added = true;     /* маркираме като добавено */
+
+    /* ❶ OSPI-heap вече съществува → изчисти „negative-cache" sentinel.
+     *    Това позволява find_ospi_area() да намери областта при
+     *    първото последващо извикване.                               */
+    if (cached_ospi_area == (mp_state_mem_area_t*)-1) {
+        cached_ospi_area = NULL;
+    }
+
     size_t blocks = bytes / BYTES_PER_BLOCK;
     mp_printf(&mp_plat_print,
               "[OSPI_GC] Init: Added %u blocks (%u MB) from 0x%08x to GC heap\n",
@@ -475,56 +543,18 @@ void gc_ospi_on_init(void) {
 
 /* ------------------------------------------------------------------+
  |  ЛЕКА СТАТИСТИКА БЕЗ ПРЕМЕСТВАНЕ НА ОБЕКТИ                        |
+ |  (Implementation moved up before first use)                       |
  +------------------------------------------------------------------*/
-typedef struct {
-    size_t total_blocks;
-    size_t free_blocks;
-    size_t free_chunks;
-} ospi_stat_t;
-
-static void ospi_collect_stats(mp_state_mem_area_t *area, ospi_stat_t *st)
-{
-    st->total_blocks = area->gc_alloc_table_byte_len * BLOCKS_PER_ATB;
-    st->free_blocks  = 0;
-    st->free_chunks  = 0;
-
-    size_t run = 0;
-    for (size_t i = 0; i < st->total_blocks; i++) {
-        if (atb_is_free(area, i)) {
-            st->free_blocks++;
-            run++;
-        } else if (run) {
-            st->free_chunks++;
-            run = 0;
-        }
-    }
-    if (run) {
-        st->free_chunks++;
-    }
-}
 
 #if OSPI_STATS_ONLY
 void gc_ospi_defrag_force(void)
 {
     mp_state_mem_area_t *ospi_area = find_ospi_area();
     if (!ospi_area) {
-        mp_printf(&mp_plat_print, "[OSPI_DEFRAG] No OSPI area found\n");
+        mp_printf(&mp_plat_print, "[OSPI_GC] No OSPI area\n");
         return;
     }
-
-    ospi_stat_t s;
-    ospi_collect_stats(ospi_area, &s);
-
-    size_t fragmentation = 0;
-    if (s.free_chunks > 0 && s.total_blocks > 0) {
-        fragmentation = (s.free_chunks * 100) / s.total_blocks;
-    }
-
-    mp_printf(&mp_plat_print,
-        "[OSPI_GC] %u free blocks in %u chunks → фрагм. %u %%\n",
-        (unsigned)s.free_blocks,
-        (unsigned)s.free_chunks,
-        (unsigned)fragmentation);
+    gc_ospi_stats_core();   /* просто покажи статистиката */
 }
 #else
 /* … ако някой ден имплементираш real compaction, сложи го тук … */
@@ -538,4 +568,6 @@ mp_obj_t gc_ospi_stats(void) {
     return mp_const_none;      // => Python ще вижда „None"
 }
 
-#endif // MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
\ No newline at end of file
+#endif /* MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC */
+
+/* end of file */
\ No newline at end of file
diff --git a/py/gc_ospi.h b/py/gc_ospi.h
index a94441e2..2ff51e4d 100644
--- a/py/gc_ospi.h
+++ b/py/gc_ospi.h
@@ -9,6 +9,7 @@
 
 void gc_ospi_init(void);
 void gc_ospi_on_init(void);
+void gc_ospi_reset(void);
 mp_obj_t gc_ospi_stats(void);
 void gc_ospi_defrag(void);
 void gc_ospi_defrag_force(void);
diff --git a/py/gc_ospi_internal.h b/py/gc_ospi_internal.h
index 9f3873c8..7c4b50e5 100644
--- a/py/gc_ospi_internal.h
+++ b/py/gc_ospi_internal.h
@@ -4,8 +4,18 @@
 #include "py/mpconfig.h"
 #if MICROPY_HW_HAS_OSPI_RAM && MICROPY_ENABLE_GC
 
+/* ------------------------------------------------------------------+
+|  Global OSPI address window – keep it defined once only            |
++------------------------------------------------------------------*/
+#ifndef OSPI_START
+#  define OSPI_START 0x68000000u      /* 8 MB external Octa-SPI RAM */
+#endif
+#ifndef OSPI_END
+#  define OSPI_END   0x68800000u
+#endif
+
 #include <stdint.h>
-#include "py/mpstate.h"
+#include "py/mpstate.h"   /* mp_state_mem_area_t */
 #include "py/gc.h"
 #include "py/gc_ospi.h"   // за да вижда публичните константи ако трябват
 #include "py/mphal.h"     // за MICROPY_BEGIN_ATOMIC_SECTION
@@ -32,7 +42,7 @@ typedef struct _ospi_chunk_t {
 typedef uint16_t block_len_t;
 #define MAX_LEN 0xFFFF  // Maximum value for 16-bit block length
 #ifndef BLOCKS_PER_ATB
-#define BLOCKS_PER_ATB  (8)
+#define BLOCKS_PER_ATB  (4)  // 4 blocks per ATB byte (2 bits per block)
 #endif
 #define BLOCK_SHIFT(b)           (2 * ((b) & (BLOCKS_PER_ATB - 1)))
 #define ATB_MASK(k,b)            ((k) << BLOCK_SHIFT(b))
