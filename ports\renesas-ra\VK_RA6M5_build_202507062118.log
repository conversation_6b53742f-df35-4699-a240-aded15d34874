Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
CC ../../lib/lwip/src/core/init.c
CC ../../lib/lwip/src/core/mem.c
../../lib/lwip/src/core/init.c:176:2: error: #error "If you want to use Sequential API, you have to define NO_SYS=0 in your lwipopts.h"
  176 | #error "If you want to use Sequential API, you have to define NO_SYS=0 in your lwipopts.h"
      |  ^~~~~
See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/lib/lwip/src/core/init.o] Error 1
make: *** Waiting for unfinished jobs....
CC ../../lib/lwip/src/core/memp.c
In file included from ../../lib/lwip/src/core/memp.c:65:
../../lib/lwip/src/include/lwip/priv/api_msg.h:95:7: error: expected specifier-qualifier-list before 'API_MSG_M_DEF_C'
   95 |       API_MSG_M_DEF_C(ip_addr_t, ipaddr);
      |       ^~~~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:101:7: error: parameter names (without types) in function declaration [-Werror]
  101 |       ip_addr_t API_MSG_M_DEF(ipaddr);
      |       ^~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:101:17: error: field 'API_MSG_M_DEF' declared as a function
  101 |       ip_addr_t API_MSG_M_DEF(ipaddr);
      |                 ^~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:102:7: error: parameter names (without types) in function declaration [-Werror]
  102 |       u16_t API_MSG_M_DEF(port);
      |       ^~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:102:13: error: field 'API_MSG_M_DEF' declared as a function
  102 |       u16_t API_MSG_M_DEF(port);
      |             ^~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:102:13: error: duplicate member 'API_MSG_M_DEF'
../../lib/lwip/src/include/lwip/priv/api_msg.h:140:7: error: expected specifier-qualifier-list before 'API_MSG_M_DEF_C'
  140 |       API_MSG_M_DEF_C(ip_addr_t, multiaddr);
      |       ^~~~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:177:3: error: parameter names (without types) in function declaration [-Werror]
  177 |   ip_addr_t API_MSG_M_DEF(addr);
      |   ^~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:177:13: error: field 'API_MSG_M_DEF' declared as a function
  177 |   ip_addr_t API_MSG_M_DEF(addr);
      |             ^~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:184:3: error: parameter names (without types) in function declaration [-Werror]
  184 |   sys_sem_t API_MSG_M_DEF_SEM(sem);
      |   ^~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:65:31: error: field 'API_MSG_M_DEF' declared as a function
   65 | #define API_MSG_M_DEF_SEM(m)  API_MSG_M_DEF(m)
      |                               ^~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:184:13: note: in expansion of macro 'API_MSG_M_DEF_SEM'
  184 |   sys_sem_t API_MSG_M_DEF_SEM(sem);
      |             ^~~~~~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:186:3: error: parameter names (without types) in function declaration [-Werror]
  186 |   err_t API_MSG_M_DEF(err);
      |   ^~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:186:9: error: field 'API_MSG_M_DEF' declared as a function
  186 |   err_t API_MSG_M_DEF(err);
      |         ^~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:65:31: error: duplicate member 'API_MSG_M_DEF'
   65 | #define API_MSG_M_DEF_SEM(m)  API_MSG_M_DEF(m)
      |                               ^~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:184:13: note: in expansion of macro 'API_MSG_M_DEF_SEM'
  184 |   sys_sem_t API_MSG_M_DEF_SEM(sem);
      |             ^~~~~~~~~~~~~~~~~
../../lib/lwip/src/include/lwip/priv/api_msg.h:186:9: error: duplicate member 'API_MSG_M_DEF'
  186 |   err_t API_MSG_M_DEF(err);
      |         ^~~~~~~~~~~~~
cc1.exe: all warnings being treated as errors
See [1;31mhttps://github.com/micropython/micropython/wiki/Build-Troubleshooting[0m
make: *** [../../py/mkrules.mk:90: build-VK_RA6M5/lib/lwip/src/core/memp.o] Error 1
