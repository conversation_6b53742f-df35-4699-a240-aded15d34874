Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
GEN build-VK_RA6M5/genhdr/qstrdefs.generated.h
CC ../../py/mpstate.c
CC ../../py/nlr.c
CC ../../py/nlrx86.c
CC ../../py/nlrx64.c
CC ../../py/nlrthumb.c
CC ../../py/nlraarch64.c
CC ../../py/nlrmips.c
CC ../../py/nlrpowerpc.c
CC ../../py/nlrxtensa.c
CC ../../py/nlrrv32.c
CC ../../py/nlrrv64.c
CC ../../py/nlrsetjmp.c
CC ../../py/malloc.c
CC ../../py/gc.c
CC ../../py/gc_ospi.c
CC ../../py/modgc_ospi.c
CC ../../py/pystack.c
CC ../../py/qstr.c
CC ../../py/vstr.c
CC ../../py/mpprint.c
CC ../../py/unicode.c
CC ../../py/mpz.c
CC ../../py/reader.c
CC ../../py/lexer.c
CC ../../py/parse.c
CC ../../py/scope.c
CC ../../py/compile.c
CC ../../py/emitcommon.c
CC ../../py/emitbc.c
CC ../../py/asmbase.c
CC ../../py/asmx64.c
CC ../../py/emitnx64.c
CC ../../py/asmx86.c
CC ../../py/emitnx86.c
CC ../../py/asmthumb.c
CC ../../py/emitnthumb.c
CC ../../py/emitinlinethumb.c
CC ../../py/asmarm.c
CC ../../py/emitnarm.c
CC ../../py/asmxtensa.c
CC ../../py/emitnxtensa.c
CC ../../py/emitinlinextensa.c
CC ../../py/emitnxtensawin.c
CC ../../py/asmrv32.c
CC ../../py/emitnrv32.c
CC ../../py/emitndebug.c
CC ../../py/formatfloat.c
CC ../../py/parsenumbase.c
CC ../../py/parsenum.c
CC ../../py/emitglue.c
CC ../../py/persistentcode.c
CC ../../py/runtime.c
CC ../../py/runtime_utils.c
CC ../../py/scheduler.c
CC ../../py/nativeglue.c
CC ../../py/pairheap.c
CC ../../py/ringbuf.c
CC ../../py/cstack.c
CC ../../py/stackctrl.c
CC ../../py/argcheck.c
CC ../../py/warning.c
CC ../../py/profile.c
CC ../../py/map.c
CC ../../py/obj.c
CC ../../py/objarray.c
CC ../../py/objattrtuple.c
CC ../../py/objbool.c
CC ../../py/objboundmeth.c
CC ../../py/objcell.c
CC ../../py/objclosure.c
CC ../../py/objcomplex.c
CC ../../py/objdeque.c
CC ../../py/objdict.c
CC ../../py/objenumerate.c
CC ../../py/objexcept.c
CC ../../py/objfilter.c
CC ../../py/objfloat.c
CC ../../py/objfun.c
CC ../../py/objgenerator.c
CC ../../py/objgetitemiter.c
CC ../../py/objint.c
CC ../../py/objint_longlong.c
CC ../../py/objint_mpz.c
CC ../../py/objlist.c
CC ../../py/objmap.c
CC ../../py/objmodule.c
CC ../../py/objobject.c
CC ../../py/objpolyiter.c
CC ../../py/objproperty.c
CC ../../py/objnone.c
CC ../../py/objnamedtuple.c
CC ../../py/objrange.c
CC ../../py/objreversed.c
CC ../../py/objringio.c
CC ../../py/objset.c
CC ../../py/objsingleton.c
CC ../../py/objslice.c
CC ../../py/objstr.c
CC ../../py/objstrunicode.c
CC ../../py/objstringio.c
CC ../../py/objtuple.c
CC ../../py/objtype.c
CC ../../py/objzip.c
CC ../../py/opmethods.c
CC ../../py/sequence.c
CC ../../py/stream.c
CC ../../py/binary.c
CC ../../py/builtinimport.c
CC ../../py/builtinevex.c
CC ../../py/builtinhelp.c
CC ../../py/modarray.c
CC ../../py/modbuiltins.c
CC ../../py/modcollections.c
CC ../../py/modgc.c
CC ../../py/modio.c
CC ../../py/modmath.c
CC ../../py/modcmath.c
CC ../../py/modmicropython.c
CC ../../py/modstruct.c
CC ../../py/modsys.c
CC ../../py/moderrno.c
CC ../../py/modthread.c
CC ../../py/vm.c
CC ../../py/bc.c
CC ../../py/showbc.c
CC ../../py/repl.c
CC ../../py/smallint.c
CC ../../py/frozenmod.c
CC build-VK_RA6M5/lvgl/lv_mpy.c
CC ../../extmod/machine_adc.c
CC ../../extmod/machine_adc_block.c
CC ../../extmod/machine_bitstream.c
CC ../../extmod/machine_i2c.c
CC ../../extmod/machine_i2s.c
CC ../../extmod/machine_mem.c
CC ../../extmod/machine_pinbase.c
CC ../../extmod/machine_pulse.c
CC ../../extmod/machine_pwm.c
CC ../../extmod/machine_signal.c
CC ../../extmod/machine_spi.c
CC ../../extmod/machine_timer.c
CC ../../extmod/machine_uart.c
CC ../../extmod/machine_usb_device.c
CC ../../extmod/machine_wdt.c
CC ../../extmod/modasyncio.c
CC ../../extmod/modbinascii.c
CC ../../extmod/modbluetooth.c
CC ../../extmod/modbtree.c
CC ../../extmod/modcryptolib.c
CC ../../extmod/moddeflate.c
CC ../../extmod/modframebuf.c
CC ../../extmod/modhashlib.c
CC ../../extmod/modheapq.c
CC ../../extmod/modjson.c
CC ../../extmod/modlwip.c
CC ../../extmod/modmachine.c
CC ../../extmod/modnetwork.c
CC ../../extmod/modonewire.c
CC ../../extmod/modos.c
CC ../../extmod/modplatform.c
CC ../../extmod/modrandom.c
CC ../../extmod/modre.c
CC ../../extmod/modselect.c
CC ../../extmod/modsocket.c
CC ../../extmod/modtls_axtls.c
CC ../../extmod/modtls_mbedtls.c
CC ../../extmod/modtime.c
CC ../../extmod/moductypes.c
CC ../../extmod/modvfs.c
CC ../../extmod/modwebrepl.c
CC ../../extmod/modwebsocket.c
CC ../../extmod/network_cyw43.c
CC ../../extmod/network_esp_hosted.c
CC ../../extmod/network_lwip.c
CC ../../extmod/network_ninaw10.c
CC ../../extmod/network_ppp_lwip.c
CC ../../extmod/network_wiznet5k.c
CC ../../extmod/os_dupterm.c
CC ../../extmod/vfs.c
CC ../../extmod/vfs_blockdev.c
CC ../../extmod/vfs_fat.c
CC ../../extmod/vfs_fat_diskio.c
CC ../../extmod/vfs_fat_file.c
CC ../../extmod/vfs_lfs.c
CC ../../extmod/vfs_posix.c
CC ../../extmod/vfs_posix_file.c
CC ../../extmod/vfs_reader.c
CC ../../extmod/virtpin.c
CC ../../shared/libc/abort_.c
CC ../../shared/libc/printf.c
CC ../../lib/oofatfs/ff.c
CC ../../lib/oofatfs/ffunicode.c
CC ../../shared/netutils/netutils.c
CC mbedtls/mbedtls_port.c
CC ../../shared/netutils/dhcpserver.c
CC ../../shared/netutils/trace.c
CC ../../shared/readline/readline.c
CC ../../shared/runtime/gchelper_native.c
CC ../../shared/runtime/interrupt_char.c
CC ../../shared/runtime/mpirq.c
CC ../../shared/runtime/pyexec.c
CC ../../shared/runtime/softtimer.c
CC ../../shared/runtime/stdout_helpers.c
CC ../../shared/runtime/sys_stdio_mphal.c
CC ../../shared/timeutils/timeutils.c
CC ../../shared/tinyusb/mp_usbd.c
CC ../../shared/tinyusb/mp_usbd_cdc.c
CC ../../shared/tinyusb/mp_usbd_descriptor.c
CC ../../drivers/bus/softspi.c
CC ../../drivers/bus/softqspi.c
CC ../../drivers/memory/spiflash.c
CC ../../drivers/dht/dht.c
CC ra/ra_adc.c
CC ra/ra_dac.c
CC ra/ra_flash.c
CC ra/ra_gpio.c
CC ra/ra_i2c.c
CC ra/ra_icu.c
CC ra/ra_init.c
CC ra/ra_int.c
CC ra/ra_rtc.c
CC ra/ra_sci.c
CC ra/ra_spi.c
CC ra/ra_timer.c
CC ra/ra_gpt.c
CC ra/ra_utils.c
CC ../../lib/tinyusb/src/class/cdc/cdc_device.c
CC ../../lib/tinyusb/src/class/dfu/dfu_rt_device.c
CC ../../lib/tinyusb/src/class/hid/hid_device.c
CC ../../lib/tinyusb/src/class/midi/midi_device.c
CC ../../lib/tinyusb/src/class/msc/msc_device.c
CC ../../lib/tinyusb/src/class/usbtmc/usbtmc_device.c
CC ../../lib/tinyusb/src/class/vendor/vendor_device.c
CC ../../lib/tinyusb/src/common/tusb_fifo.c
CC ../../lib/tinyusb/src/device/usbd.c
CC ../../lib/tinyusb/src/device/usbd_control.c
CC ../../lib/tinyusb/src/portable/renesas/rusb2/dcd_rusb2.c
CC ../../lib/tinyusb/src/portable/renesas/rusb2/hcd_rusb2.c
CC ../../lib/tinyusb/src/portable/renesas/rusb2/rusb2_common.c
CC ../../lib/tinyusb/src/tusb.c
CC boardctrl.c
CC main.c
CC ra_it.c
CC rng.c
CC mphalport.c
CC mpnetworkport.c
CC mpthreadport.c
CC irq.c
CC pendsv.c
CC systick.c
CC powerctrl.c
CC powerctrlboot.c
CC pybthread.c
CC factoryreset.c
CC timer.c
CC led.c
CC uart.c
CC gccollect.c
CC help.c
CC machine_dac.c
CC machine_i2c.c
CC machine_spi.c
CC machine_pin.c
CC machine_rtc.c
CC machine_sdcard.c
CC network_lan.c
CC eth.c
CC extint.c
CC usrsw.c
CC flash.c
CC flashbdev.c
CC storage.c
CC fatfs_port.c
CC usbd.c
CC boards/VK_RA6M5/board_init.c
CC build-VK_RA6M5/pins_VK_RA6M5.c
CC build-VK_RA6M5/frozen_content.c
  GEN     objects.rsp
LINK build-VK_RA6M5/firmware.elf
GEN build-VK_RA6M5/firmware.hex
GEN build-VK_RA6M5/firmware.bin
