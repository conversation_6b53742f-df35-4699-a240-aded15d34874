Use make V=1 or set BUILD_VERBOSE in your environment to increase build verbosity.
GEN build-VK_RA6M5/genhdr/qstr.i.last
GEN build-VK_RA6M5/genhdr/qstr.split
GEN build-VK_RA6M5/genhdr/moduledefs.split
GEN build-VK_RA6M5/genhdr/root_pointers.split
GEN build-VK_RA6M5/genhdr/compressed.split
GEN build-VK_RA6M5/genhdr/qstrdefs.collected.h
GEN build-VK_RA6M5/genhdr/moduledefs.collected
GEN build-VK_RA6M5/genhdr/root_pointers.collected
GEN build-VK_RA6M5/genhdr/compressed.collected
QSTR not updated
Module registrations not updated
Root pointer registrations not updated
Compressed data not updated
CC boards/VK_RA6M5/ra_gen/hal_data.c
  GEN     objects.rsp
LINK build-VK_RA6M5/firmware.elf
GEN build-VK_RA6M5/firmware.hex
GEN build-VK_RA6M5/firmware.bin
